<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Validate_Account_Activation_Start_Date</fullName>
    <active>true</active>
    <description>Ensures that the Account Activation Start Date cannot be set to a date in the past, today, or on a Monday when Status Code is not C. If you want to set the Account Activation Start Date to Monday, please enter the previous day, Sunday.</description>
    <errorConditionFormula>NOT(ISPICKVAL(JJ_JPN_StatusCode__c, &apos;C&apos;)) &amp;&amp; (Account_Activation_Start_Date__c &lt;= TODAY() || MOD(Account_Activation_Start_Date__c - DATE(1900,1,7), 7) = 1)</errorConditionFormula>
    <errorDisplayField>Account_Activation_Start_Date__c</errorDisplayField>
    <errorMessage>The Account Activation Start Date cannot be set to a date in the past, today, or on a Monday.
If you want to set the Account Activation Start Date to Monday, please enter the previous day, Sunday.</errorMessage>
</ValidationRule>
