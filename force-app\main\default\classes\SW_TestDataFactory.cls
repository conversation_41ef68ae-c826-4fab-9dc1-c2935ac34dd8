/**
* File Name: SW_TestDataFactory  
* @description : TestDataFactory for Skywalker Application 
* Copyright : <PERSON> & <PERSON>
* <AUTHOR> Shobana K G | <EMAIL> | <EMAIL>
* 
* Modification Log 
* =============================================================== 
*  Ver  |Date         |Author             	|Modification
*  1.0  |10-Apr-2019  |<EMAIL>   	|New Class created
*  1.1  |27-Mar-2020  |@<EMAIL>  	|Added AATB-7144 changes
*  1.2  |19-Nov-2020  |@<EMAIL>  	|Added methods - createAccounts,createJJVProContacts,createRoles 
* 											 and createJJVCPortalSettings  
* 	1.3  |27-Jun-2021  |<EMAIL>|Changes in Create Account(Hospital) per AGFB-6295
*  1.4  |08-July-2021 |<EMAIL>|Changes in createDummyHospital,createWorkConts,createDoctorsfromWkconts per AGFB-6722
*  1.5  |30-March-2022 |<EMAIL>!Changes for test data for contentversion

*/
@isTest
public class SW_TestDataFactory {

   public static final String ESCALATION_PERSON =  'Escalation person';
    public static final String PERSON_IN_CHARGE = 'Person in charge';
    public static final String SECOND_PERSON_IN_CHARGE ='Second person in charge';
    public static final String MANAGER ='Manager';
    public static final String CONTACT_PERSON = 'Contact person';
    public static final String ALL_DAY_LACREON ='All Day Comfort With Lacreon';
    public static final String TEST = 'Test_';
    public static final String SUBJECT = 'Subject';
	public static final String TEST_ACCOUNT = 'Test Account';
	public static final String HAVISH_TEST = 'Havish Test';
	public static final String NUM4 = '4';
	public static final String AMERICA_LOSANGELES = 'America/Los_Angeles';
	public static final String JNJ_DOT_COM = '@jnj.com';
	private static final String UNITS_OF_MEASURE = '"unitsofMeasure":"km",';
    private static final String COUNTRY_CODE = '"countryCode":';
    private static final String TREATMENT_OPTIONS = '"treatmentOptions":';
    private static final String WEB_PROPERTIES = '"webProperties":';
	public static Date todayDate = getValidActivationDate();

    /**
     * @description Helper method to get a valid activation date for test data
     * Returns a future date that avoids validation rule restrictions
     * @return Date A valid activation date for testing
     */
    public static Date getValidActivationDate() {
        Date testDate = Date.today() + 2; // Start with day after tomorrow

        // If it's a Monday, move to Tuesday
        DateTime testDateTime = DateTime.newInstance(testDate, Time.newInstance(0, 0, 0, 0));
        if (testDateTime.format('E') == 'Mon') {
            testDate = testDate + 1;
        }

        return testDate;
    }

    // Create Accounts
    public static List<Account> createAccounts(Integer numAccts) {
        List<Account> accList = new List<Account>();        
        for (Integer i=0;i<numAccts;i++) {
            Account acc = new Account(Name='TestAccount' + i);
            accList.add(acc);
        }
        // Added for AATB-7144
        accList.add(new Account(Name='ＶＣＣ＿ＪＰＮ＿Ｄｕｍｍｙ変更・削除不可', OutletNumber__c = '39322', CountryCode__c = 'JPN'));
        insert accList;
        return accList;
    }
    
    // Create Contacts
    public static List<Contact> createContacts(Integer numCont, List<Account> accList) {
        List<Contact> conList = new List<Contact>();        
        for (Integer i=0; i<numCont; i++) {
            Contact con = new Contact(LastName = 'TestContact' + i, Email = 'TestContact' + i + JNJ_DOT_COM, AccountId = accList[i].Id);
            conList.add(con);
        }
        insert conList;
        return conList;
    }
    //AGFB-11997 Create contacts with Record type
    public static List<Contact> createContacts(Integer numCont, String recTypeDevName) {
        Id recTypeId = SW_SObjectsSelector.fetchRecordTypeByName('Contact',recTypeDevName)[0].Id;
        List<Contact> conList = new List<Contact>();        
        for (Integer i=0; i<numCont; i++) {
            Contact con = new Contact(LastName = 'TestContact' + i, Email = 'TestContact' + i + JNJ_DOT_COM,RecordTypeId = recTypeId);
            conList.add(con);
        }
        insert conList;
        return conList;
    }
    // Create Accounts
    public static List<Account> createAccounts(Integer numAccts, String recTypeDevName, String countryCode) {
        Id recTypeId = SW_SObjectsSelector.fetchRecordTypeByName('Account',recTypeDevName)[0].Id;
        List<Account> accList = new List<Account>();        
        for (Integer i=1; i<=numAccts; i++) {
            Account acc = new Account(Name='TestAccount '+i, RecordTypeId = recTypeId, CountryCode__c = countryCode);
            accList.add(acc);
        }
        insert accList; 
        return accList;
    }
    
    // Create Accounts with best visit time
    public static List<Account> createAccountWithBestVisitTime(Integer numAccts)
    {
         List<Account> accList = new List<Account>();
         for (Integer i=1; i<=numAccts; i++) {
         Account acc = new Account(Name='TestAccount'+i,OutletNumber__c='ECP12343',Phone='*********',SW_Clrvw_BestDayVisit__c='Monday;Tuesday;Wednesday;Thursday;Friday;Saturday',SW_Clrvw_BestTimeVisit__c='PM',SW_Clrvw_Operating_Hours__c='both');
         accList.add(acc);
         }
         insert accList;
         return accList;
    }
    
    // Create JJVPro Contacts as well as Account Contact Roles (created through ContactHandler.cls)
    public static List<Contact> createJJVProContacts(List<Account> acctList) {         
        List<Contact> contList = new List<Contact>();
        List<Role__c> roleList = new List<Role__c>(createRoles());        
        Id recTypeId = SW_SObjectsSelector.fetchRecordTypeByName('Contact','HCP_Contacts')[0].Id;
        createJJVCPortalSettings();
        
        for (Account acct : acctList) {
            for (Role__c role : roleList) {
                Contact cont = new Contact(LastName = acct.Name +' - TestContact', RecordTypeId = recTypeId, AccountId = acct.Id, User_Role__c = role.Id);
                contList.add(cont);                
            }            
        }
        insert contList;
        return contList;  
    }
    
    // Create Role__c records
    public static List<Role__c> createRoles() {
        List<String> roleTypesList = new List<String>();
        List<Role__c> rolesList = new List<Role__c>();
        
        Schema.DescribeFieldResult fieldResult = Role__c.Type__c.getDescribe();
        List<Schema.PicklistEntry> ple = fieldResult.getPicklistValues();
        for (Schema.PicklistEntry pickVal : ple) {
            roleTypesList.add(pickVal.getLabel());
        }
        
        for (String roleType : roleTypesList) {
            Role__c role = new Role__c(Type__c = roleType);
            rolesList.add(role);
        }
        insert rolesList;
        return rolesList;    
    }
    
    // Create records of type Portal_Settings__c
    public static void createJJVCPortalSettings() {
        List<Portal_Settings__c> portalSettingList = new List<Portal_Settings__c>();
        Account genericAcc = new Account(Name = 'Generic Professional Account');
        insert genericAcc; 
        String genAccId = [SELECT Id FROM Account WHERE Name = 'Generic Professional Account' LIMIT 1][0].Id;
        Portal_Settings__c pSetting = new Portal_Settings__c(Name = 'GenericAccount Id', Value__c = genAccId);
        portalSettingList.add(pSetting);
        insert portalSettingList;
    }   
    
    // Create Events
    public static List<Event> createEvents(List<Account> accList, Integer numEvts, String recordType) {
        List<Event> evtList = new List<Event>();
        List<RecordType> rtList = [SELECT Id FROM RecordType WHERE SObjectType = 'Event' and DeveloperName =: recordType LIMIT 1];        
        for (Account acc : accList) {
            for (Integer i=0;i<numEvts;i++) {
                Event evt = new Event(Subject = 'TestSubject' + i + '_' + acc.Name, DurationInMinutes = 60, ActivityDateTime=datetime.now(),EndDateTime=datetime.now().addMinutes(60),
                                      IsRecurrence = false, RecordTypeId = rtList[0].Id);
                evt.StartDateTime=evt.ActivityDateTime;
                evtList.add(evt);
            }
        }
        insert evtList;
        return evtList;
    }
    
    // Create records of type Event_RecordType_Setting__c
    public static List<Event_RecordType_Setting__c> createRecordTypeSetting() {
        List<Event_RecordType_Setting__c> recTypSettingList = new List<Event_RecordType_Setting__c>();
        Event_RecordType_Setting__c recTypSetting = new Event_RecordType_Setting__c(Composite_Key__c = 'JJ_JPN_CustomerDevelopmentManagerCallJPN#Subject#en_US',
                                                                                    Default_Value__c = 'Call', Field_Api__c = 'Subject', 
                                                                                    Record_Type__c = 'JJ_JPN_CustomerDevelopmentManagerCallJPN', 
                                                                                    Label_Value_pairs__c = 'Call#Call', SW_Language__c = 'en_US');
        recTypSettingList.add(recTypSetting);
        //Added for AATB-5370
        Event_RecordType_Setting__c recTypSetting2 = new Event_RecordType_Setting__c(Composite_Key__c = 'Liaison_JPN#Subject#en_US',Default_Value__c = 'Call LIAISON Unison', Field_Api__c = 'Subject', 
                                                                                     Record_Type__c = 'Liaison_JPN',Values__c ='Call LIAISON Solo',
                                                                                     Label_Value_pairs__c = 'Call LIAISON Unison#Call LIAISON Unison,Call LIAISON Solo#Call LIAISON Solo',
                                                                                     SW_Language__c = 'en_US');
        recTypSettingList.add(recTypSetting2);
		Event_RecordType_Setting__c recTypSetting3 = new Event_RecordType_Setting__c(Composite_Key__c = 'JJ_JPN_CustomerDevelopmentManagerCallJPN#Subject#ja',	
                                                                                        Default_Value__c = 'Call', Field_Api__c = 'Subject', 	
                                                                                        Record_Type__c = 'JJ_JPN_CustomerDevelopmentManagerCallJPN', 	
                                                                                        Label_Value_pairs__c = 'Call#Call', SW_Language__c = 'ja');	
        recTypSettingList.add(recTypSetting3);	
        Event_RecordType_Setting__c recTypSetting4 = new Event_RecordType_Setting__c(Composite_Key__c = 'Commercial_JPN#Subject#ja',	
                                                                                        Default_Value__c = 'CT訪問', Field_Api__c = 'Subject', 	
                                                                                        Record_Type__c = 'Commercial_JPN', 	
                                                                                     	Label_Value_pairs__c = 'CT訪問#CT訪問', SW_Language__c = 'ja');	
        recTypSettingList.add(recTypSetting4);
        
        Event_RecordType_Setting__c recTypSetting5 = new Event_RecordType_Setting__c(Composite_Key__c = 'Telesales_JPN#Subject#ja',
                                                                                     Default_Value__c = 'Teleセールス', Field_Api__c =SUBJECT, 
                                                                                     Record_Type__c = 'Telesales_JPN', 
                                                                                     Label_Value_pairs__c = 'Teleセールス#Call Telesales', SW_Language__c = 'ja');
        recTypSettingList.add(recTypSetting5);
        
        Event_RecordType_Setting__c recTypSetting6 = new Event_RecordType_Setting__c(Composite_Key__c = 'Telesupport_Call_JPN#Subject#ja',
                                                                                     Default_Value__c = 'TEL サポート', Field_Api__c = SUBJECT, 
                                                                                     Record_Type__c = 'Telesupport_Call_JPN', 
                                                                                     Label_Value_pairs__c = 'FS訪問#Call,Email#Email,会議(社内)#Meeting,Send Letter/Quote#Send Letter/Quote,その他#Other,TEL サポート#TEL Support', SW_Language__c = 'ja');
        recTypSettingList.add(recTypSetting6);
        insert recTypSettingList;
        return recTypSettingList; 
    }
    // Implemented for AATB-5433
    // Create Accounts with Sales Rep Alias field
    public static List<Account> createAccountsWithSalesRep(Integer numofRec,Boolean Active){
        String orgId = UserInfo.getOrganizationId();
        String dateString = String.valueof(Datetime.now()).replace(' ','').replace(':','').replace('-','');
        Integer randomInt = Integer.valueOf(math.rint(math.random()*1000000));
        String uniqueName = orgId + dateString + randomInt;
        List<User> userList = new List<User>(); 
        List<Profile> profile = [SELECT Id FROM Profile WHERE Name = 'ASPAC KR Country Champion' LIMIT 1];
        List<UserRole> roles = [SELECT Id, Name FROM UserRole where Name ='KR - Country Champion' LIMIT 1];
        for (Integer i=0;i<numofRec;i++) {
            User user = new User(firstname = 'fName' + i,
                                 lastName = 'lName' + i,
                                 email = uniqueName + '@test' + i +orgId + '.org',
                                 Username = uniqueName + '@test' + i + orgId + '.org',
                                 EmailEncodingKey = 'ISO-8859-1',
                                 Alias = 'Alias' + i,
                                 TimeZoneSidKey = AMERICA_LOSANGELES,
                                 LocaleSidKey = 'en_US',
                                 LanguageLocaleKey = 'en_US',
                                 ProfileId = profile[0].Id,
                                 UserRoleId = roles[0].Id,
                                 Unique_User_Id__c = 'UserId' + i,
                                 IsActive = Active);           
            userList.add(user);
        }
        insert userList; 
        List<Account> accountList = new List<Account>();
        for(Integer j=0; j<numofRec; j++){
            Account account = new Account(Name='TestAccount' + j, SalesRep__c = userList[j].Unique_User_Id__c,CreatedById = UserInfo.getUserId());
            accountList.add(account); 
        }  
        insert accountList;
        return accountList;
    }
    
    // Create records of type SW_CalendarSharing__c and Event
    public static List<SW_CalendarSharing__c> createCalSharing(List<Id> userIds) {
        List<SW_CalendarSharing__c> calSharingList = new List<SW_CalendarSharing__c>();
        SW_CalendarSharing__c calSharing = new SW_CalendarSharing__c(SW_LoginUser__c = userIds[0], SW_SharedUser__c = userIds[0],
                                                                     SW_Checked__c = true, SW_Color__c = '#6D92B3',
                                                                     SW_UniqueKey__c = userIds[0] + '-' + userIds[0]);
        calSharingList.add(calSharing);
        insert calSharingList;
        return calSharingList; 
    }
    
    // Create dummy hospital
    // Modified for AGFB-6722
    public static void createDummyHospital() {
        RecordType hospRecType = [SELECT Id FROM RecordType WHERE SobjectType = 'Account' and DeveloperName = 'JJ_JPN_Hospital' LIMIT 1];
        insert new Account(Name = 'ダミー病院', JJ_JPN_ULTHospitalCode__c = '9999999', RecordTypeId = hospRecType.Id);
    }
    
    /**
* Description - Create JJ_JPN_Work_Hospital__c records - Added for AATB-6021
* Input - Integer - number of records to be created
- String - Abbrevation Facitily Name Kanji field value
- String - Representative Code field value
* Output - List of JJ_JPN_Work_Hospital__c records
*/
    public static List<JJ_JPN_Work_Hospital__c> createWorkHosps(Integer numWorkHosp, String AbbFacilityNameKanji,String repCode){
        List<JJ_JPN_Work_Hospital__c> wHList = new List<JJ_JPN_Work_Hospital__c>();        
        for (Integer i=0;i<numWorkHosp;i++) {
            JJ_JPN_Work_Hospital__c wH = new JJ_JPN_Work_Hospital__c(
                JJ_JPN_sishospitalcode__c ='1000'+i,
                JJ_JPN_ulthospitalcode__c = '1000'+i,
                JJ_JPN_abbreviatedfacilitynamekanji__c = AbbFacilityNameKanji,
                JJ_JPN_abbreviatedfacilitynamekana__c = 'Test AbbFacNameKana'+i,
                JJ_JPN_officialfacilitynamekanji__c = 'Test OffFacNameKanji'+i,
                JJ_JPN_officialfacilitynamekana__c ='Test OffFacNameKana'+i,
                JJ_JPN_addresspostcode__c = '50001'+i,
                JJ_JPN_addresskanji__c ='北海道札幌市ＸＸ区ＡＢＣ町１２３',
                JJ_JPN_telephoneNumber__c = '7854120366'+i,
                JJ_JPN_representativenamekanji__c = 'Test RepNameKanji'+i,
                JJ_JPN_representativecodepersonalcode__c = repCode,
                JJ_JPN_representativenamekana__c = 'Test RepNameKana'+i,
                JJ_JPN_prefecturecode__c = 'p000'+i,
                JJ_JPN_citywardtownvillagecode__c = 'TestCWTV'+i,
                JJ_JPN_addressblockcode1__c = 'AC001'+i,
                JJ_JPN_addressblockcode2__c = 'AC002'+i,
                JJ_JPN_approvedbedcounttotal__c = '100'+i
            );
            wHList.add(wH);
        }
        insert wHList;
        return wHList;
        
    }
    /**
* Description - Create Account(Hospital) records - Added for AATB-6021
* Output - List of Account(Hospital) records
* Added JJ_JPN_ULTHospitalCode__c field in query as per AGFB-6295
*/
    public static List<Account> createHospitalsfromWkHosp(){
        List<RecordType> recordType = SW_SObjectsSelector.fetchRecordTypeByName('Account', 'JJ_JPN_Hospital');
        List<Account> hospList = new List<Account>();        
        for (JJ_JPN_Work_Hospital__c workHospital : [select Id,JJ_JPN_sishospitalcode__c,JJ_JPN_ulthospitalcode__c,JJ_JPN_abbreviatedfacilitynamekanji__c,
                                                     JJ_JPN_representativecodepersonalcode__c,JJ_JPN_approvedbedcounttotal__c from
                                                     JJ_JPN_Work_Hospital__c]) {
                                                         System.debug('workHospital '+workHospital);
                                                         Account hospital = new Account(
                                                             RecordTypeId = recordType[0].Id,
                                                             Name = workHospital.JJ_JPN_abbreviatedfacilitynamekanji__c,
                                                             JJ_JPN_SISHospitalCode__c = workHospital.JJ_JPN_sishospitalcode__c,
                                                             JJ_JPN_ULTHospitalCode__c = workHospital.JJ_JPN_ulthospitalcode__c,
                                                             JJ_JPN_ULTRepresentativeNameCode__c = workHospital.JJ_JPN_representativecodepersonalcode__c,
                                                             JJ_JPN_ULTApprovedbedcounttotal__c = workHospital.JJ_JPN_approvedbedcounttotal__c);
                                                         hospList.add(hospital);
                                                         System.debug('hospList '+hospList);
                                                     }
        System.debug('hospList '+hospList);
        Database.SaveResult[] results = Database.insert(hospList); 
        System.debug('hospital results '+results);
        //insert hospList;
        return hospList;
        
    }
    /**
* Description - Create JJ_JPN_Work_Contact__c records - Added for AATB-6022
* Input - Integer - number of records to be created
Integer - number of records with different ULThospitalcodes
- String - Doctor Name Kanji field value
* Output - List of JJ_JPN_Work_Contact__c records
* Modified createWorkConts Method to add ULThospitalcodes Field per AGFB -6722

*/
    public static List<JJ_JPN_Work_Contact__c> createWorkConts(Integer numWorkCont,Integer numULTcodes,String doctorNameKanji){
        List<JJ_JPN_Work_Contact__c> wCList = new List<JJ_JPN_Work_Contact__c>();        
        for (Integer i=0;i<numWorkCont;i++) {
            for(Integer j=0;j<numULTcodes;j++){
                JJ_JPN_Work_Contact__c wC = new JJ_JPN_Work_Contact__c(
                    JJ_JPN_doctorcode__c ='3000'+i,
                    JJ_JPN_doctornamekanji__c = doctorNameKanji,
                    JJ_JPN_doctornamekana__c ='Test DrNameKana'+i,
                    JJ_JPN_gender__c ='M',
                    JJ_JPN_dateofbirthyear__c='01/01/1978',
                    JJ_JPN_hometownname__c = 'Test homTown'+i,
                    JJ_JPN_almamatername__c ='Test almamatername'+i,
                    JJ_JPN_graduationyear__c ='199'+i,
                    JJ_JPN_registrationyear__c ='200'+i,
                    JJ_JPN_medicaldepartment1name__c ='Test medicaldepartment1name'+i,
                    JJ_JPN_medicaldepartment2name__c = 'Test medicaldepartment2name'+i,
                    JJ_JPN_medicaldepartment3name__c = 'Test medicaldepartmen3name'+i,
                    JJ_JPN_medicaldepartment4name__c = 'Test medicaldepartment4name'+i,
                    JJ_JPN_medicaldepartment5name__c = 'Test medicaldepartment5name'+i,
                    JJ_JPN_sishospitalcode__c = '1000'+j,
                    JJ_JPN_ulthospitalcode__c = '1000'+j 
                );
                wCList.add(wC);
            }
        }
        insert wCList;
        return wCList;
        
    }
    /**
* Description - Create JJ_JPN_Work_DoctorConference__c records - Added for AATB-6022
* Input - Integer - number of records to be created
* Output - List of JJ_JPN_Work_DoctorConference__c records
*/
    public static List<JJ_JPN_Work_DoctorConference__c> createDrConf(Integer numDrConf){
        List<JJ_JPN_Work_DoctorConference__c> drConfList = new List<JJ_JPN_Work_DoctorConference__c>();        
        for (Integer i=0;i<numDrConf;i++) {
            JJ_JPN_Work_DoctorConference__c drCon = new JJ_JPN_Work_DoctorConference__c(
                JJ_JPN_doctorcode__c ='3000'+i,
                JJ_JPN_conferencenamekanji__c='Test ConfNameKanji'+i
                
            );
            drConfList.add(drCon);
        }
        insert drConfList;
        return drConfList;
        
    }
    /**
* Description - Create JJ_JPN_Work_DSpecialistConference__c records - Added for AATB-6022
* Input - Integer - number of records to be created
* Output - List of JJ_JPN_Work_DSpecialistConference__c records
*/
    public static List<JJ_JPN_Work_DSpecialistConference__c> createDrSpecConf(Integer numDrSpecConf){
        List<JJ_JPN_Work_DSpecialistConference__c> drSpecConfList = new List<JJ_JPN_Work_DSpecialistConference__c>();        
        for (Integer i=0;i<numDrSpecConf;i++) {
            JJ_JPN_Work_DSpecialistConference__c drSpecCon = new JJ_JPN_Work_DSpecialistConference__c(
                JJ_JPN_doctorcode__c ='3000'+i,
                JJ_JPN_specialistnamekanji__c='Test SpecialistNameKanji'+i
                
            );
            drSpecConfList.add(drSpecCon);
        }
        insert drSpecConfList;
        return drSpecConfList;
        
    }
    /**
* Description - Create JJ_JPN_Work_Workplace__c records - Added for AATB-6022
* Input - Integer - number of records to be created
* Output - List of JJ_JPN_Work_Workplace__c records
*/
    public static List<JJ_JPN_Work_Workplace__c> createWrPlace(Integer numWrPlace){
        List<JJ_JPN_Work_Workplace__c> wrPlaceList = new List<JJ_JPN_Work_Workplace__c>();        
        for (Integer i=0;i<numWrPlace;i++) {
            JJ_JPN_Work_Workplace__c wP = new JJ_JPN_Work_Workplace__c(
                JJ_JPN_doctorcode__c ='3000'+i,
                JJ_JPN_departmentaffiliationcode__c='2000'+i
            );
            wrPlaceList.add(wP);
        }
        insert wrPlaceList;
        return wrPlaceList;
        
    }
    /**
* Description - Create JJ_JPN_Work_Codelist__c records - Added for AATB-6022
* Input - Integer - number of records to be created
* Output - List of JJ_JPN_Work_Codelist__c records
*/
    public static List<JJ_JPN_Work_Codelist__c> createWrCodeList(Integer numWrCode){
        List<JJ_JPN_Work_Codelist__c> wrCodeList = new List<JJ_JPN_Work_Codelist__c>();        
        for (Integer i=0;i<numWrCode;i++) {
            JJ_JPN_Work_Codelist__c wrCode = new JJ_JPN_Work_Codelist__c(
                JJ_JPN_code__c='2000'+i,
                JJ_JPN_namekanji__c ='Test NameKanji'+i,
                JJ_JPN_layouttypedimsid__c ='7.0'
            );
            wrCodeList.add(wrCode);
        }
        insert wrCodeList;
        return wrCodeList;
        
    }
    /**
* Description - Create Contact(Doctor) records - Added for AATB-6022
* Output - List of Contact(Doctor) records
* Modified createDoctorsfromWkconts Method to add ULThospitalcodes Field per AGFB -6722
*/
    public static List<Contact> createDoctorsfromWkconts(){
        Map<String,Set<String>> docULTcodeMap = new Map<String,Set<String>>();
        Map<String,Account> accountMap= new Map <String,Account>();
        List<Account> hospitalList = [select Id,Name,JJ_JPN_SISHospitalCode__c,JJ_JPN_ULTHospitalCode__c,JJ_JPN_ULTRepresentativeNameCode__c,JJ_JPN_ULTApprovedbedcounttotal__c from Account where RecordType.DeveloperName = 'JJ_JPN_Hospital'];
        List<JJ_JPN_Work_Contact__c> workContactList  = [select Id,Name,JJ_JPN_doctorcode__c,JJ_JPN_sishospitalcode__c ,JJ_JPN_ulthospitalcode__c from JJ_JPN_Work_Contact__c];
        List<RecordType> recordType = SW_SObjectsSelector.fetchRecordTypeByName('Contact', 'JJ_JPN_Doctor');
        List<Contact> docList = new List<Contact>();
        if(!hospitalList.isEmpty()){
            for(Account hosp : hospitalList){
                accountMap.put(hosp.JJ_JPN_ULTHospitalCode__c, hosp);
            }
        }
        if(!workContactList.isEmpty()){
            for(JJ_JPN_Work_Contact__c WrkCont : workContactList){
                if(!docULTcodeMap.containsKey(WrkCont.JJ_JPN_doctorcode__c)){
                    docULTcodeMap.put(WrkCont.JJ_JPN_doctorcode__c, new Set<String>{WrkCont.JJ_JPN_ulthospitalcode__c});
                    //sisHospCodes.add(WrkCont.JJ_JPN_ulthospitalcode__c);
                }
                else{
                    docULTcodeMap.get(WrkCont.JJ_JPN_doctorcode__c).add(WrkCont.JJ_JPN_ulthospitalcode__c);
                    //sisHospCodes.add(WrkCont.JJ_JPN_ulthospitalcode__c);
                }                
            }
        }
        for (JJ_JPN_Work_Contact__c workContact : [select Id,JJ_JPN_doctorcode__c,JJ_JPN_sishospitalcode__c,JJ_JPN_ulthospitalcode__c, JJ_JPN_doctornamekanji__c
                                                   from JJ_JPN_Work_Contact__c]) {
                                                       String lastName ='';
                                                       String firstName ='';
                                                       String accName = null;
                                                       if(String.isNotBlank(workContact.JJ_JPN_doctornamekanji__c)){
                                                           lastName = workContact.JJ_JPN_doctornamekanji__c.substringBefore('\u3000');
                                                           firstName = workContact.JJ_JPN_doctornamekanji__c.substringAfter('\u3000');
                                                       }
                                                       if(docULTcodeMap.containsKey(workContact.JJ_JPN_doctorcode__c))	{
                                                           Integer bedCount =0;
                                                           Set<String> ultCodes = docULTcodeMap.get(workContact.JJ_JPN_doctorcode__c);
                                                           if(ultCodes.size()>1){
                                                               for(String ultCode : ultCodes){
                                                                   if(accountMap.containsKey(ultCode))	{
                                                                       if(String.isNotBlank(accountMap.get(ultCode).JJ_JPN_ULTRepresentativeNameCode__c) && 
                                                                          accountMap.get(ultCode).JJ_JPN_ULTRepresentativeNameCode__c == workContact.JJ_JPN_doctorcode__c){
                                                                              accName = accountMap.get(ultCode).Id;	
                                                                          }
                                                                       else if(String.isNotBlank(accountMap.get(ultCode).JJ_JPN_ULTApprovedbedcounttotal__c) &&
                                                                               Integer.valueOf(accountMap.get(ultCode).JJ_JPN_ULTApprovedbedcounttotal__c) >bedCount){
                                                                                   bedCount = Integer.valueOf(accountMap.get(ultCode).JJ_JPN_ULTApprovedbedcounttotal__c) ;	
                                                                                   accName  = accountMap.get(ultCode).Id;
                                                                               }                              
                                                                   }
                                                                   
                                                               }
                                                           }
                                                           else if(ultCodes.size()==1 && accountMap.containsKey(new List<String>(ultCodes)[0])){
                                                               accName = accountMap.get(new List<String>(ultCodes)[0]).Id;
                                                           }      
                                                       }
                                                       Contact doctor = new Contact(
                                                           RecordTypeId = recordType[0].Id,
                                                           LastName = lastName,
                                                           FirstName = firstName,
                                                           AccountId = accName,
                                                           JJ_JPN_ULTDoctorCode__c = workContact.JJ_JPN_doctorcode__c);
                                                       docList.add(doctor);
                                                   }
        //insert docList;
        Database.SaveResult[] results = Database.insert(docList); 
        System.debug('docList results '+results);
        return docList;
        
    }
    
    // Create Address
    public static List<JJ_JPN_Address__c> createAddress(Integer numAddrs) {
        List<JJ_JPN_Address__c> addrList = new List<JJ_JPN_Address__c>();        
        for (Integer i=0;i<numAddrs;i++) {
            JJ_JPN_Address__c acc = new JJ_JPN_Address__c(JJ_JPN_City__c = 'TestCity' + i, JJ_JPN_PostalCodes__c = '000' + i,
                                                          JJ_JPN_StateJapan__c = 'TestStateJapan' + i,
                                                          JJ_JPN_Street__c = 'TestStreet' + i);
            addrList.add(acc);
        }
        insert addrList;
        return addrList;
    }
    // Create DocuSignTemplate
    public static List<SW_DocuSignTemplate__c> createDocuSignTemplate(String templateId,String selector)
    {
        List<SW_DocuSignTemplate__c> docSignTemplates = new List<SW_DocuSignTemplate__c>();
        SW_DocuSignTemplate__c template = new SW_DocuSignTemplate__c(Name = templateId, SW_Selector__c = selector);
        docSignTemplates.add(template);
        insert docSignTemplates;
        return docSignTemplates;
    }
    
    //Start of AGFB-9432
    // Create CollaborationGroup 
    public static CollaborationGroup createCollaborationGroup()
    {   
        CollaborationGroup groupe = new CollaborationGroup(Name='Group1',CollaborationType='Public');
        insert groupe;
        return groupe;
    }    
     
    // Create ContentVersion
    public static ContentDocumentLink createContentVersion(String Linkedid)
    {   
        
        List<ContentVersion> ContentVersionList = new List<ContentVersion>();
        ContentVersion conVersion = new ContentVersion(Title = 'Penguins',PathOnClient = 'Penguins.jpg',VersionData = Blob.valueOf('Test Content'),IsMajorVersion = true);
        ContentVersionList.add(conVersion);
        insert ContentVersionList;
        if(!ContentVersionList.isEmpty())
        {
            if (ContentVersionList[0].id == null || Linkedid == null) { return null; }
            ContentDocumentLink cdl = new ContentDocumentLink();
            cdl.ContentDocumentId = [SELECT ContentDocumentId 
                                     FROM ContentVersion 
                                     WHERE Id =: ContentVersionList[0].id].ContentDocumentId;
            cdl.LinkedEntityId = Linkedid;
            cdl.ShareType = 'V';
            try {
                insert cdl;
                return cdl;
            } catch(DMLException e) {
                System.debug(e);
                return null;
            }
        }
        
        return null;
    } 
    //End of AGFB-9432
    //AGFB-9477
    //Create POA
    public static List<SW_Clrvw_POA__c> createPOA(Integer numOfPOAs){
        List<SW_Clrvw_POA__c> poaList=new List<SW_Clrvw_POA__c>();
        for(Integer i=0;i<=numOfPOAs;i++){
            SW_Clrvw_POA__c poa=new SW_Clrvw_POA__c();
            poa.Name='ANZ POA'+i;
            poa.SW_Clrvw_POACycle__c='POA 1';
            poa.SW_Clrvw_StartDate__c=Date.today();
            poa.SW_Clrvw_EndDate__c=Date.today().addYears(1);
            poa.SW_Clrvw_Status__c='Active';
            poaList.add(poa);
        }
        insert poaList;
        return poaList;
    }
    //AGFB-9477
    //Create POA Objective
    public static List<SW_Clrvw_POAObjective__c> createPOAObjectives(string poaId,Integer numOfObjectives){
        List<SW_Clrvw_POAObjective__c> poaObjectives=new List<SW_Clrvw_POAObjective__c>();
        for(Integer i=0;i<=numOfObjectives;i++){
            SW_Clrvw_POAObjective__c poaObjective=new SW_Clrvw_POAObjective__c();
            poaObjective.Name='Objective'+i;
            poaObjective.SW_ClrvwObjective_No__c=i+1;
            poaObjective.SW_Clrvw_POA__c=poaId;
            poaObjective.SW_Clrvw_ApplicableTo__c='Key Account Only';
            poaObjectives.add(poaObjective);
        }
        insert poaObjectives;
        return poaObjectives;
    }
    
    //AGFB-9432	
    //Create POA Activities
    public static List<SW_Clrvw_POAActivity__c> createPOAActivities(string objId,Integer numOfActivities){
        List<SW_Clrvw_POAActivity__c> poaAvtivities = new List<SW_Clrvw_POAActivity__c>();
        for(Integer i = 0; i < numOfActivities; i++){
            SW_Clrvw_POAActivity__c poaActivity = new SW_Clrvw_POAActivity__c();
            poaActivity.Name = 'Objective'+i;
            poaActivity.SW_Clrvw_ActivityNo__c = i+1;
            poaActivity.SW_Clrvw_POAObjective__c = objId;
            poaAvtivities.add(poaActivity);
        }
        insert poaAvtivities;
        return poaAvtivities;
    }
    //AGFB-9424
    //create AccountPOAMapping
    public static List<SW_Clrvw_AccountPOAMapping__c> createPOAAccountMapping(Id accountId,Id poaId,List<SW_Clrvw_POAObjective__c> poaObjectives){
        List<SW_Clrvw_AccountPOAMapping__c> accountPOAMappings=new List<SW_Clrvw_AccountPOAMapping__c>();
        SW_Clrvw_AccountPOAMapping__c accountPOAMapping=new SW_Clrvw_AccountPOAMapping__c();
        accountPOAMapping.SW_Clrvw_POA__c=poaId;
        accountPOAmapping.SW_Clrvw_Account__c=accountId;
        accountPOAmapping.SW_Clrvw_IsActive__c=true;
        for(Integer i=0;i<poaObjectives.size();i++){
            accountPOAMapping.put('SW_Clrvw_POAObjective'+(i+1)+'__c',poaObjectives[i].Id);
        }
        accountPOAMappings.add(accountPOAMapping);
        insert accountPOAMappings;
        return accountPOAMappings;
    }
    
    public static List<SW_Clrvw_EventCall__c> createEventCall(List<Id> EventIds){
        List<SW_Clrvw_EventCall__c> eventCallList = new List<SW_Clrvw_EventCall__c>();
        for(integer i=0; i<EventIds.size();i++)
        {
            SW_Clrvw_EventCall__c eventCall = new SW_Clrvw_EventCall__c();
            eventCall.SW_Crvw_RelatedEventId__c = EventIds[i];
            eventCallList.add(eventCall);
        }
        insert eventCallList;
        return eventCallList;
    }
    
    //AGFB-10193
    public static list<SW_Clrvw_AccountPOAStatus__c> createAccountPOAStatus(String activityId, String accId, Integer numOfRecords){
        list<SW_Clrvw_AccountPOAStatus__c> accountPOAList = new list<SW_Clrvw_AccountPOAStatus__c>();
        for(Integer i = 0; i < numOfRecords; i++){
            SW_Clrvw_AccountPOAStatus__c accPOAVar = new SW_Clrvw_AccountPOAStatus__c();
            accPOAVar.SW_Clrvw_Account__c = accId;
            accPOAVar.SW_Clrvw_POAActivity__c = activityId;
            accPOAVar.SW_Clrvw_Status__c = true;
            accountPOAList.add(accPOAVar);
        }
        insert accountPOAList;
        return accountPOAList;
    }
    
    // Create ContentDocument with Content Version
	
	//Create Test simple Account
    public static Account createSingleBasicAccount(){
        Account acc=new Account(Name='Test1');
        insert acc;
        return acc;
    }
    
	/**
	* @description  Create Test Account CMR related.
	* @return Account returns account created
	*/
    public static Account createSingleAccount() {
        Account acc = New Account(Name =HAVISH_TEST,PublicAddress__c='Bangalore', 
                                  SalesRep__c = 'ABC', AccountNumber = '123457', 
                                  OutletNumber__c = '123457', CountryCode__c = 'Japan',
                                  Subscribe__c=true, custGroup__c = HAVISH_TEST, 
                                  JJ_JPN_Second_person_in_charge__c = HAVISH_TEST);
        insert acc;
        return acc;
    }
    //Create Test Call JPN Event
    public static Event createCallJPNEvent(Account acc) {
        Id callJPNId = Schema.SObjectType.Event.getRecordTypeInfosByName().get('Call JPN').getRecordTypeId();
        event e= new event(StartDateTime = date.today(), EndDateTime = date.today().addDays(2), Subject = TEST + acc.Name,
                           IsRecurrence = false, WhatId = acc.Id, In_Store_Campaign_Activated__c = SECOND_PERSON_IN_CHARGE,
                           In_Store_Inventory__c = SECOND_PERSON_IN_CHARGE, RecordTypeId = callJPNId);
        insert e;
        
        event event= new event(StartDateTime = date.today(), EndDateTime = date.today().addDays(2), Subject = TEST + acc.Name,
                               IsRecurrence = false, WhatId = acc.Id, In_Store_Campaign_Activated__c = MANAGER,
                               In_Store_Inventory__c = MANAGER, RecordTypeId = callJPNId);
        insert event;
        return e;
    }
    //Create Test Commercial JPN Event
    public static Event createCommercialJPNEvent(Account acc) {
        Id commJPNId = Schema.SObjectType.Event.getRecordTypeInfosByName().get('Commercial JPN').getRecordTypeId();
        event e= new event(StartDateTime = date.today(), EndDateTime = date.today().addDays(2), Subject = TEST + acc.Name,
                           IsRecurrence = false, WhatId = acc.Id, In_Store_Campaign_Activated__c =PERSON_IN_CHARGE,
                           In_Store_Inventory__c =PERSON_IN_CHARGE, RecordTypeId = commJPNId);
        insert e;
        return e;
    }
	
    /**
	* @description Method to createLiaisonEvent.
	* @return event
	*/
    public static Event createLiaisonJPN(Account acc) {
        Id lisionJPNId = Schema.SObjectType.Event.getRecordTypeInfosByName().get('Liaison JPN').getRecordTypeId();
        event e= new event(StartDateTime = date.today(), EndDateTime = date.today().addDays(2), Subject = 'TestSubject1' + '_' + acc.Name,
                           IsRecurrence = false, WhatId = acc.Id, In_Store_Campaign_Activated__c = SECOND_PERSON_IN_CHARGE,
                           In_Store_Inventory__c = SECOND_PERSON_IN_CHARGE, RecordTypeId = lisionJPNId);
        insert e;
        return e;
    }
	    
    //Create Test Telesales JPN  Event Person_in_charge
    public static Event createTelesalesJPNEvent(Account acc) {
        Id TelesalesJPNId = Schema.SObjectType.Event.getRecordTypeInfosByName().get('Telesales JPN').getRecordTypeId();
        event e= new event(StartDateTime = date.today(), EndDateTime = date.today().addDays(2), Subject = TEST + acc.Name,
                           IsRecurrence = false, WhatId = acc.Id, In_Store_Campaign_Activated__c = PERSON_IN_CHARGE,
                           In_Store_Inventory__c = PERSON_IN_CHARGE, RecordTypeId = TelesalesJPNId, Patient1__c=ALL_DAY_LACREON);
        insert e;
        
        //Create Test Telesales JPN  Event Second_person_in_charge  
        event event1= new event(StartDateTime = date.today(), EndDateTime = date.today().addDays(2), Subject = TEST + acc.Name,
                                IsRecurrence = false, WhatId = acc.Id, In_Store_Campaign_Activated__c =SECOND_PERSON_IN_CHARGE,
                                In_Store_Inventory__c =SECOND_PERSON_IN_CHARGE, RecordTypeId = TelesalesJPNId, Patient1__c=ALL_DAY_LACREON);
        insert event1;
        
        //Event Contact_person
        event event= new event(StartDateTime = date.today(), EndDateTime = date.today().addDays(2), Subject =TEST + acc.Name,
                               IsRecurrence = false, WhatId = acc.Id, In_Store_Campaign_Activated__c =CONTACT_PERSON,
                               In_Store_Inventory__c =CONTACT_PERSON, RecordTypeId = TelesalesJPNId, Patient1__c=ALL_DAY_LACREON);
        insert event; 
        
        event event2= new event(StartDateTime = date.today(), EndDateTime = date.today().addDays(2), Subject = TEST + acc.Name,
                                IsRecurrence = false, WhatId = acc.Id, In_Store_Campaign_Activated__c =ESCALATION_PERSON,
                                In_Store_Inventory__c = ESCALATION_PERSON, RecordTypeId = TelesalesJPNId, Patient1__c=ALL_DAY_LACREON);
        insert event2;
        return event2;
    }
	  //Create Test Telesupport JPN  Event with Person_in_charge
    public static Event createTelesupportJPNEvent(Account acc) {
        Id TelesupportJPNId = Schema.SObjectType.Event.getRecordTypeInfosByName().get('Telesupport Call JPN').getRecordTypeId();
        event e= new event(StartDateTime = date.today(), EndDateTime = date.today().addDays(2), Subject = TEST + acc.Name,
                           IsRecurrence = false, WhatId = acc.Id, In_Store_Campaign_Activated__c = PERSON_IN_CHARGE,
                           In_Store_Inventory__c = PERSON_IN_CHARGE, RecordTypeId = TelesupportJPNId);
        insert e;
        
        //Create Test Telesales JPN  Event with Second_person_in_charge
        event event1= new event(StartDateTime = date.today(), EndDateTime = date.today().addDays(2), Subject = TEST + acc.Name,
                                IsRecurrence = false, WhatId = acc.Id, In_Store_Campaign_Activated__c =SECOND_PERSON_IN_CHARGE,
                                In_Store_Inventory__c =SECOND_PERSON_IN_CHARGE, RecordTypeId = TelesupportJPNId);
        insert event1;
        
        //Create Test Telesales JPN  Event with Contact_person
        event event2= new event(StartDateTime = date.today(), EndDateTime = date.today().addDays(2), Subject = TEST + acc.Name,
                                IsRecurrence = false, WhatId = acc.Id, In_Store_Campaign_Activated__c =CONTACT_PERSON,
                                In_Store_Inventory__c =CONTACT_PERSON, RecordTypeId = TelesupportJPNId);
        insert event2;
        //Create Test Telesales JPN  Event with Escalation_person
        event event3= new event(StartDateTime = date.today(), EndDateTime = date.today().addDays(2), Subject = TEST + acc.Name,
                                IsRecurrence = false, WhatId = acc.Id, In_Store_Campaign_Activated__c = ESCALATION_PERSON,
                                In_Store_Inventory__c = ESCALATION_PERSON, RecordTypeId = TelesupportJPNId);
        insert event3;
        return event3;
    }
    
     public static user ManagerJPNUser(){
        Profile p = [SELECT Id FROM Profile WHERE Name='System Administrator' LIMIT 1];
        User u = new User(Alias = 'test', Email='<EMAIL>',
                          EmailEncodingKey='UTF-8', LastName='Manag', LanguageLocaleKey='ja',
                          LocaleSidKey='ja', ProfileId = p.Id, FirstName='Trest',
                          TimeZoneSidKey=AMERICA_LOSANGELES, UserName='<EMAIL>',Unique_User_Id__c = 'ABC', Country = 'Japan');
        insert u;
        return u;
    }
    //Create jpn test user
    public static List<User> createJPNUser(Integer numOfUsers){
        Profile p = [SELECT Id FROM Profile WHERE Name='System Administrator' LIMIT 1];
        User test1=ManagerJPNUser();
		List<User> usrList = new List<User>();
		
		for(integer i=0;i<numOfUsers;i++)
        {
          string emailVar = 'standarduser'+i+'@testorg.com';
             String UsrNameVar= 'havish'+i+'@abc.com';
        usrList.add(new User(Alias = 'standt', Email=emailVar,
                          EmailEncodingKey='UTF-8', LastName='Havish', LanguageLocaleKey='ja',
                          LocaleSidKey='ja', ProfileId = p.Id, FirstName='Trest',ManagerId=test1.id,
                          TimeZoneSidKey=AMERICA_LOSANGELES, UserName=UsrNameVar,Unique_User_Id__c = 'ABC', Country = 'Japan'));
						  }
        insert usrList;
        return usrList;
    }

	// Create ContentDocument with Content Version
    public static String createContentDocumnet(String Linkedid)
    {   
        List<ContentVersion> ContentVersionList = new List<ContentVersion>();
        ContentVersion conVersion = new ContentVersion(Title = 'Penguins',PathOnClient = 'Penguins.jpg',VersionData = Blob.valueOf('Test Content'),IsMajorVersion = true);
        ContentVersionList.add(conVersion);
        try{
            insert ContentVersionList;
            if(!ContentVersionList.isEmpty()){
                String documentId = [SELECT ContentDocumentId 
                                         FROM ContentVersion 
                                         WHERE Id =: ContentVersionList[0].id].ContentDocumentId;    
                String fileDate = '[{"contentVersionId": "'+ContentVersionList[0].id +'","documentId": "'+documentId+'"}]';
                return fileDate;
                
            }
        }
        catch(DMLException e) {
            System.debug(e);
            return null;
        }
        
        return null;
    } 
	

	public static JJ_JPN_CustomerMasterRequest__c createSinglecmr() {
        JJ_JPN_CustomerMasterRequest__c cmr=new JJ_JPN_CustomerMasterRequest__c(Name='Test',JJ_JPN_PayerCode__c='1234',JJ_JPN_AccountType__c='Field Sales(FS)',JJ_JPN_PayerNameKanji__c='漢字',JJ_JPN_Information2WDefine__c='1',
                                                                                JJ_JPN_ApprovalStatus__c = 'Approved', JJ_JPN_StatusCode__c ='C',Account_Activation_Start_Date__c = todayDate,JJ_JPN_BillToCode__c='12345',
                                                                                JJ_JPN_SoldToCode__c='12346',JJ_JPN_InterfaceCheck__c=4,JJ_JPN_SubCustomerGroup__c='SCG000010');
        
        insert cmr;
        return cmr;
    }

    public static List<EventRelation> createEventRelation(List<Contact> conList, Event evt) {
        List<EventRelation> listToInsert = new List<EventRelation>();
        for(Contact con: conList){
            listToInsert.add(new EventRelation(EventId=evt.id,RelationId=con.id));
        }
        insert listToInsert;
        return listToInsert;
    }
    
    public static List<SW_Clrvw_POACallRelation__c> createPOACallRelation(List<SW_Clrvw_POAActivity__c> poaActivity, Id evtCall) {
        List<SW_Clrvw_POACallRelation__c> listToInsert = new List<SW_Clrvw_POACallRelation__c>();
        for(SW_Clrvw_POAActivity__c poaAct: poaActivity){
            listToInsert.add(new SW_Clrvw_POACallRelation__c(SW_Clrvw_EventCall__c=evtCall,SW_Clrvw_POAActivity__c=poaAct.id, SW_Clrvw_IsActive__c = true));
        }
        insert listToInsert;
        return listToInsert;
    }
    public static list<Coaching__c> createCoachingRecord(Integer count){
        List<Coaching__c> listToInsert = new List<Coaching__c>();
        for(Integer i = 1 ; i<= count ; i++){
            listToInsert.add(new Coaching__c(Name = 'Test '+i, SW_Clrvw_AssessmentType__c = 'Eye to Eye', Coachee__c = Userinfo.getUserId()));
        }
        insert listToInsert;
        return listToInsert;
    }

    /** 
	*  @description create Dealer JPN User 
	*  @param numUsr number of users to be created
    *  @return List<User> returns user record
    */
    public static List<User> createJPNDealerUser(Integer numUsr){
        Account acc = new Account();
        List<User> usrList = new List<User>();
        List<Contact> cList = new List<Contact>();
        createRecordTypeSetting();
        acc = createSingleAccount();
        cList = createDealerSalesContact(numUsr,acc);
        Profile p = [SELECT Id FROM Profile WHERE Name='ASPAC JPN Dealer Sales Member' LIMIT 1];
        for (Integer i=0; i<numUsr; i++) {
        User u = new User(Alias = 'twotwon', Email='<EMAIL>', OutletNumber__c = '123457',
                          EmailEncodingKey='UTF-8', LastName='Towtwoneo', LanguageLocaleKey='ja',
                          LocaleSidKey='ja', ProfileId = p.Id, FirstName='Twotwoone', contactId = cList[i].Id,
                          TimeZoneSidKey=AMERICA_LOSANGELES, UserName='<EMAIL>'+i,Unique_User_Id__c = 'tentenneo'+i, Country = 'Japan');
        usrList.add(u);
        }
        
        insert usrList;
        return usrList;
    }
    /** 
	*  @description create Dealer Sales Contact
	*  @param numCont number of contacts to be created
	*  @param accList account to be associated with
    *  @return List<Contact> returns List of contacts created
    */
    public static List<Contact> createDealerSalesContact(Integer numCont, Account accList) {
        List<Contact> conList = new List<Contact>();
        Id recTypeId = SW_SObjectsSelector.fetchRecordTypeByName('Contact', 'Dealer_Sales')[0].Id;
        for (Integer i=0; i<numCont; i++) {
            Contact con = new Contact(RecordTypeId = recTypeId, LastName = 'TestContact' + i, Email = 'TestContact' + i + JNJ_DOT_COM, accountId = accList.Id);
            conList.add(con);
        }
        insert conList;
        return conList;
    }

/**
* @description  creates CMR related account
* @return Account returns account created
*/	
    public static Account createCMRaccount() {
        Account acc = New Account(Name = TEST_ACCOUNT,PublicAddress__c='Bangalore', 
                                  SalesRep__c = 'ABC', AccountNumber = '123457', 
                                  OutletNumber__c = '12345', CountryCode__c = 'Japan',JJ_JPN_Information2WDefine__c='1',
                                  Subscribe__c=true, custGroup__c = HAVISH_TEST, 
                                  JJ_JPN_Second_person_in_charge__c = HAVISH_TEST,
								  JJ_JPN_ReservedCampaignFlag8__c = NUM4);
        insert acc;
        return acc;
    }
	
	//create contact ECP record type
	public static Id getECPContactsRecordtypeId()
	{
		List<JJVC_Contact_Recordtype__mdt> recordTypeList =
		[
				SELECT MasterLabel
				FROM JJVC_Contact_Recordtype__mdt
				WHERE DeveloperName = 'ECP_Contacts_RecordtypeId' LIMIT 1
		];
		return recordTypeList.isEmpty() ? '' : recordTypeList[0].MasterLabel;
	}
	
	//Create SYA Account for Singapore
	public static Account CreateSYAAccount(String recTypeDevName)
	{
		Id recTypeId = SW_SObjectsSelector.fetchRecordTypeByName('Account', recTypeDevName)[0].Id;
		Account accountRecord = new Account();
		accountRecord.RecordTypeId = recTypeId;
		accountRecord.Name = 'SG Generic Professional Account';
		accountRecord.OutletNumber__c = '12345';
		accountRecord.CountryCode__c = 'SGP';
		Insert accountRecord;
		return accountRecord;
	}
	
	//Create SYA Account for Hong Kong
	public static Account CreateSYAAccountHK(String recTypeDevName)
	{
		Id recTypeId = SW_SObjectsSelector.fetchRecordTypeByName('Account', recTypeDevName)[0].Id;
		Account accountRecord = new Account();
		accountRecord.RecordTypeId = recTypeId;
		accountRecord.Name = 'HK Generic Professional Account';
		accountRecord.OutletNumber__c = '12346';
		accountRecord.CountryCode__c = 'HKG';
		Insert accountRecord;
		return accountRecord;
	}

	//Create SYA Campaign
	public Static Campaign createTestCampaign()
	{
		Campaign testCampaign = new Campaign();
		testCampaign.Name = 'Myopia ECP Lead Gen';
		testCampaign.Type = 'Signup/Trial';
		testCampaign.isActive = true;
		insert testCampaign;
		return testCampaign;
	}

	//Create SYA Contact
	public static Contact createSYAContacts(Id accId, String Email, String Phone)
	{
		//Inserting contact
		Contact con = new Contact();
		con.FirstName = 'Fname';
		con.LastName = 'Lname';
		con.AccountId = accId;
		con.Email = Email;
		con.phone = Phone;
		insert con;
		return con;
	}
/** 
*@description This method create products	
*@param numOfProducts number of products to be created
*@return List<Product2> List of products createDealerSalesContact
**/
	public static List<Product2> createProducts(Integer numOfProducts) {
        List<Product2> prodList = new List<Product2>();
        string prodCode, prodname;
        for (Integer i=0;i<numOfProducts;i++) {
            prodCode = 'ZC306' + i;
            prodname = 'test' + i;
            Product2 prod = new Product2(IsActive = true,ProductCode__c = prodCode, Name = prodname,JJ_JPN_Brand_Description__c=prodname,JJ_JPN_Material_Group_5__c='tt',JJ_JPN_Material_Group_5_Description__c=prodname,JJ_JPN_Unit_of_Order__c=5,JJ_JPN_CountryCode__c='JPN',ProductCode=prodCode);
            prodList.add(prod);
        }
        insert prodList;
        return prodList;
    }
	public static pricebook__c createpricebookRec(PriceBook2 pricebook) {
        pricebook__c pricebookRec= new pricebook__c();
        pricebookRec.Name='TestPriceBook';
        pricebookRec.PriceBookId__c=pricebook.Id;
        insert pricebookRec;
        return pricebookRec;
    }
	public static PricebookEntry createPricebookEntry(PriceBook2 pricebook,Product2 Prod) {
        PricebookEntry pricebookEntry= new PricebookEntry();
        pricebookEntry.Pricebook2Id=pricebook.id;
        pricebookEntry.Product2Id=Prod.Id;
        pricebookEntry.UnitPrice=1000;
        pricebookEntry.IsActive=true;
        insert pricebookEntry;
        return pricebookEntry;
    } 
    public static List<Order> createOrders(Integer numOfOrders,Account Acc,Pricebook2 pricebook,Contact cont) {
        List<Order> OrderList = new List<Order>();
        for (Integer i=0;i<numOfOrders;i++) {
            OrderList.add(new Order(AccountId=Acc.id,Flag_of_Send_to_SAP__c='1',Status='Draft',MA2_Country_Code__c='HKG',MA2_Order_Number__c='HKG-********',EffectiveDate=Date.today(),MA2_Contact_Mobile_Phone__c='123456',Pricebook2Id=pricebook.id,MA2_Order_Status__c='Pending ECP Review',MA2_OrderCreatedBy__c='canvas_LANDO',ShipToContactId=cont.id));
        }
        insert OrderList;
        return OrderList;
    }
    public static List<OrderItem> createOrderItems(Integer numOfOrderItems,List<Product2> prodList,Order ordobj,PricebookEntry pricebookEntry) {
        List<OrderItem> OrderItemList = new List<OrderItem>();
        for (Integer i=0;i<numOfOrderItems;i++) {
            OrderItemList.add(new OrderItem(Product2Id=prodList[i].id,Quantity=2,OrderId=ordobj.Id,PricebookEntryId=pricebookEntry.id,UnitPrice=2,Description='test'));
        }
        insert OrderItemList;
        return OrderItemList;
    }
	public static ContentVersion createVersionWithContentDocId(Id conDocId,Blob bodyBlob)
    {
        ContentVersion cv=new ContentVersion(ContentDocumentId = conDocId,Title = 'StringFile', PathOnClient = 'StringFile.jpg', VersionData = bodyBlob, origin = 'H');
        insert cv;
        return cv;
    }
	
	/**
	* @description Method to create Test Account with Sales Rep En name and Second Person in charge.
	* @return acc this method returns created Account
	*/
    public static Account createAccountWithPersonIncharge() {
        User queryUser = ManagerJPNUser();
        Account acc = New Account(Name =TEST_ACCOUNT,PublicAddress__c='Bangalore', 
                                  SalesRep__c = 'ABC', AccountNumber = '123457', 
                                  OutletNumber__c = '123457', CountryCode__c = 'Japan',
                                  Subscribe__c=true, custGroup__c = queryUser.Name, 
                                  JJ_JPN_PersonInchargeName__c = queryUser.Id,
                                  ownerId = queryUser.Id,
                                  JJ_JPN_SecondPersonInCharge__c = queryUser.Id,
                                  JJ_JPN_ContactPersonIncharge__c = queryUser.Id,
                                  JJ_JPN_EscalationPersonInchargeName__c = queryUser.Id);
        insert acc;
        return acc;
    }
	
	/**
	* @description Method to create Test Commercial JPN Event.
	* @param acc This account will be used to create event.
	* @return Event This method returns created event
	*/
    public static Event createTimeoffTerritoryJPNEvent(Account acc) {
        Date wkStart = Date.newInstance(2023,10,12);
		Datetime wkStart2 = Datetime.newInstance(wkStart,Time.newInstance(10,0,0,0));
        Id totJPNId = Schema.SObjectType.Event.getRecordTypeInfosByDeveloperName().get('SW_Clrvw_Time_Off_Territory').getRecordTypeId();
        event evt= new event(StartDateTime = wkStart2, EndDateTime = wkStart2.addDays(2), Subject = TEST + acc.Name,
                           IsRecurrence = false, WhatId = acc.Id, RecordTypeId = totJPNId);
        insert evt;
        return evt;
    }
    
    /*
     * @description Create Test simple Account
     * @param parentId parent id for child account creation
     * @return Account returns created account 
    */
    public static Account createChildBasicAccount(Id parentId){
        Account acc=new Account(Name='Test1',parentId=parentId);
        insert acc;
        return acc;
    }
	
	/*
     * @description create taiwan test user
     * @param numOfUsers number of users to be created
     * @return List<User> returns created List of users 
    */
    public static List<User> createNonJpnUser(Integer numOfUsers){
        Profile p = [SELECT Id FROM Profile WHERE Name='System Administrator' LIMIT 1];
		List<User> usrList = new List<User>();
		for(integer i=0;i<numOfUsers;i++)
        {
          string emailVar = 'standarduser'+i+'@testorg.com';
             String usrNameVar= 'havish'+i+'@abc.com';
        usrList.add(new User(Alias = 'standt', Email=emailVar,
                          EmailEncodingKey='UTF-8', LastName='Havish', LanguageLocaleKey='ja',
                          LocaleSidKey='ja', ProfileId = p.Id, FirstName='Trest',
                          TimeZoneSidKey=AMERICA_LOSANGELES, UserName=usrNameVar,Unique_User_Id__c = 'ABC', Country = 'Taiwan'));
						  }
        insert usrList;
        return usrList;
    }

	/*
     * @description create cloudsign control records
	 * @param numOfRec number of records to be created
     * @param acc account to be used
     * @return List<CloudSign__CloudSignControl__c> returns created List of users 
    */

    public static List<CloudSign__CloudSignControl__c> createCloudSignControl(Account acc, Integer numOfRec)
    {
        List<CloudSign__CloudSignControl__c> clList = new List<CloudSign__CloudSignControl__c>();
        for(integer i=0; i<numOfRec; i++)
        {
            clList.add(new CloudSign__CloudSignControl__c(JJ_JPN_RecipientAccount__c=acc.Id));
        }
        insert clList;
        return clList;
    }

 
/*
* @methodname: createUsersRelatedByRoleHierarchy
* @description create Users Related By RoleHierarchy
* @return List<User> returns created List of users 
*/    
    public static List<User> createUsersRelatedByRoleHierarchy() {
        
        UserRole r = new UserRole(Name = 'ASPAC JPN DM Kanto2');
        insert r;
        UserRole r2 = new UserRole(Name = 'ASPAC JPN Sales Rep Kanto2', ParentRoleId = r.Id);
        insert r2;
        Profile p = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1];
        User u = new User(Alias = 'standt', Email = '<EMAIL>',
                          EmailEncodingKey = 'UTF-8', LastName = 'Testing1', LanguageLocaleKey = 'en_US',
                          LocaleSidKey = 'en_US', ProfileId = p.Id, UserRoleId = r.Id,Unique_User_Id__c='UserId0',
                          TimeZoneSidKey = 'America/Los_Angeles', UserName = 'abc343@xyz.com1',IsActive = true);
        insert u;
        User u2 = new User(Alias = 'standt', Email = '<EMAIL>',Unique_User_Id__c='UserId1',
                           EmailEncodingKey = 'UTF-8', LastName = 'Testing2', LanguageLocaleKey = 'en_US',
                           LocaleSidKey = 'en_US', ProfileId = p.Id, UserRoleId = r2.Id,
                           TimeZoneSidKey = 'America/Los_Angeles', UserName = 'abc24@xyz.com2',IsActive = true);
                           insert u2;
                           List<User> usrList = new List<User>();
                           usrList.add(u);
                           usrList.add(u2);
                           return usrList;
                           }
    
/*
* @description Create nba Record
* @param numberOfRecs list of records to be created
* @return List<JJ_JPN_NBA_Recommend__c> returns created List of JJ_JPN_NBA_Recommend__c
*/     
    public static List<JJ_JPN_NBA_Recommend__c> createNbaRecord(Integer numberOfRecs)
    {
        List<JJ_JPN_NBA_Recommend__c> nbaList = new List<JJ_JPN_NBA_Recommend__c>();
        for (Integer i=0;i<numberOfRecs;i++) {
            JJ_JPN_NBA_Recommend__c nbaRec = new JJ_JPN_NBA_Recommend__c(JJ_JPN_RecommendCategory__c=(i+1),JJ_JPN_Activity_Date__c = Date.today() ,JJ_JPN_Outlet_Number__c ='12345',JJ_JPN_ProductService__c = 'xyz',JJ_JPN_NoOfBox__c = i);
            nbaList.add(nbaRec);       
        }
        insert nbaList;
        return nbaList;
    }

	/**
	* @description creates Account Record to be used in test classes for SGP
	* @return Account record.
	*/
	public static Account createAccountECPL() 
	{
		Account myAccount = new Account(
			Name = 'Web Location Account',
			Geolocation__Latitude__s = 	1.2707282,
			Geolocation__Longitude__s = 103.8098786,
			PublicAddress__c = '	460 ALEXANDRA RD, #02-02 ALEXANDRA RETAIL CENTRE (ARC), SINGAPORE 119963',
			PublicZone__c = 'Harbourfront, Pasir Panjang, Telok Blangah',
			CountryCode__c = 'SGP',
			AccountNumber = '0*********',
			ActiveYN__c = true,
			RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Store').getRecordTypeId()
			);
		insert myAccount;
		return myAccount;
	}

	/**
	* @description creates Account Record to be used in test classes for HKG
	* @return Account record.
	*/
	public static Account createAccountECPLHKG() 
	{
		Account myAccount = new Account(
			Name = 'Web Location Account',
			Geolocation__Latitude__s = 	1.2707282,
			Geolocation__Longitude__s = 103.8098786,
			PublicAddress__c = '	460 ALEXANDRA RD, #02-02 ALEXANDRA RETAIL CENTRE (ARC), SINGAPORE 119963',
			PublicZone__c = 'Harbourfront, Pasir Panjang, Telok Blangah',
			CountryCode__c = 'HKG',
			AccountNumber = '**********',
			ActiveYN__c = true,
			RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Store').getRecordTypeId()
		);
		insert myAccount;
		return myAccount;
	}


	/** @description creates web location Record to be used in test classes.
	* @param myAccount to pass Account id
	* @return web Location record.
	*/
	public static Web_Location__c createWebLoc1(Id myAccount) 
	{
		Web_Location__c webLoc1 = new Web_Location__c(
			Display_Name__c = 'Test Display name',
			Account__c = myAccount,
			Email__c = '<EMAIL>',
			Phone__c = '**********',
			Website__c = 'www.seeyourabiliti.com',
			Attribute1__c = 'Sunday|12:00 AM-12:30 PM',
			Attribute2__c = 'Monday|08:00 AM-12:30 PM',
			Attribute3__c = 'Tuesday|12:00 AM-12:30 PM',
			Attribute4__c = 'Wednesday|12:00 AM-17:30 PM',
			Attribute5__c = 'Thursday|12:00 AM-12:30 PM',
			Attribute6__c = 'Friday|12:00 AM-12:30 PM',
			Attribute7__c = 'Saturday|12:00 AM-12:30 PM',
			treatment_options__c = 'Abiliti™ Overnight',
			Web_Properties__c = 'seeyourabiliti.com'
		);
		insert webLoc1;
		return webLoc1;
	}

	/** @description creates web location Record to be used in test classes.
	* @param myAccount to pass Account Id.
	* @return web Location record.
	*/
	public static Web_Location__c createWebLoc2(Id myAccount) 
	{
		Web_Location__c webLoc1 = new Web_Location__c(
			Display_Name__c = 'Test Display name Hk',
			Account__c = myAccount,
			Email__c = '<EMAIL>',
			Phone__c = '**********',
			Website__c = 'www.seeyourabiliti.com',
			Attribute1__c = 'Sunday|12:00 AM-12:30 PM',
			Attribute2__c = 'Monday|08:00 AM-12:30 PM',
			Attribute3__c = 'Tuesday|12:00 AM-12:30 PM',
			Attribute4__c = 'Wednesday|12:00 AM-17:30 PM',
			Attribute5__c = 'Thursday|12:00 AM-12:30 PM',
			Attribute6__c = 'Friday|12:00 AM-12:30 PM',
			Attribute7__c = 'Saturday|12:00 AM-12:30 PM',
			treatment_options__c = 'Abiliti™ Overnight',
			Web_Properties__c = 'seeyourabiliti.com'
		);
		insert webLoc1;
		return webLoc1;
	}

	/** @description JsonRequest1 for AccountsECPLocator to be used in test classes.
	* @param countryCode
	* @param treatmentOptions
	* @param webProperties
	* @return web Location Response.
	*/
	public static String getJsonRequest(String countryCode, String treatmentOptions, String webProperties ) 
	{
		return '{' +
			'"longitude":"103.8098786",' +
			'"latitude":"1.2707282",' +
			UNITS_OF_MEASURE +
			'"radius":"10",' +
			COUNTRY_CODE + countryCode + ',' +
			TREATMENT_OPTIONS  + treatmentOptions + ',' +
			WEB_PROPERTIES  + webProperties + ' ' +
			'}';
	}

	/** @description JsonRequest1 for AccountsECPLocator to be used in test classes.
	* @param countryCode
	* @param treatmentOptions
	* @param webProperties
	* @return web Location Response.
	*/
	public static String getJsonRequest2(String countryCode, String treatmentOptions, String webProperties ) 
	{
		return '{' +
			'"longitude":"103.8098786",' +
			'"latitude":"1.2707282",' +
			UNITS_OF_MEASURE +
			COUNTRY_CODE + countryCode + ',' +
			TREATMENT_OPTIONS  + treatmentOptions + ',' +
			WEB_PROPERTIES  + webProperties + ' ' +
			'}';
	}

	/** @description JsonRequest1 for AccountsECPLocator to be used in test classes.
	* @param countryCode
	* @param treatmentOptions
	* @param webProperties
	* @return web Location Response.
	*/
	public static String getJsonRequest3(String countryCode, String treatmentOptions, String webProperties ) 
	{
		return '{' +
			'"longitude":"103.7469611",' +
			'"latitude":"1.3839803",' +
			UNITS_OF_MEASURE +
			'"radius":"1",' +
			COUNTRY_CODE + countryCode + ',' +
			TREATMENT_OPTIONS  + treatmentOptions + ',' +
			WEB_PROPERTIES  + webProperties + ' ' +
			'}';
	}
	
	/** @description Insert user to be used in test classes.
	 * @param userName 
	 * @param contactRec
	 * @param profileId
	 * @return users.
	 */
    public static User insertUser(String userName, Contact contactRec, Id profileId)
	{
		User usr = new User();
		usr.Title = 'Mr.';
		usr.FirstName = 'Acuvue';
		usr.LastName = 'Pro user';
		usr.Email = (username == null) ? '<EMAIL>' : username;
		usr.UserName = (username == null) ? '<EMAIL>' : username;
		usr.Communication_Agreement__c = true;
		usr.Localesidkey = 'en_US';
		usr.Languagelocalekey = 'en_US';
		usr.EmailEncodingKey = 'ISO-8859-1';
		usr.Alias = (usr.LastName.length() > 7) ? usr.LastName.substring(0, 7) : usr.LastName;
		usr.TimeZoneSidKey = 'America/New_York';
		usr.NPINumber__c = 'TESTPRO01235';
		usr.ContactId = contactRec != null ? contactRec.Id : null;
		usr.ProfileId = profileId;
		insert usr;
		return usr;
	}
    
    /** @description Insert Integration user to be used in test classes.
	 * @param userName 
	 * @param profileName
	 * @return integration user.
	 */
    public static User insertIntegrationUser(String userName, String profileName)
	{
		Profile integrationProfile = [SELECT Id FROM Profile WHERE Name = :profileName LIMIT 1];
		User integrationUser = SW_TestDataFactory.insertUser(username, null, integrationProfile.Id);
		return integrationUser;
	}
}