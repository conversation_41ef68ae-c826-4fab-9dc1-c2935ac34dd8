<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Decision_Early_Exit_Check</name>
        <label>Early Exit Check</label>
        <locationX>176</locationX>
        <locationY>287</locationY>
        <defaultConnectorLabel>Nothing To Process - Exit</defaultConnectorLabel>
        <rules>
            <name>Continue_Processing</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Formula_Something_To_Process</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Decision_JPN_Company_Name_Update</targetReference>
            </connector>
            <label>Continue Processing</label>
        </rules>
    </decisions>
    <decisions>
        <name>Decision_JPN_Company_Name_Update</name>
        <label>JPN Company Name Update Check</label>
        <locationX>176</locationX>
        <locationY>503</locationY>
        <defaultConnector>
            <targetReference>Decision_ShippingCountry_Clear</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Company Name Update</defaultConnectorLabel>
        <rules>
            <name>Update_JPN_Company_Name_Account_Name_Changed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Formula_Account_Name_Changed</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_JPN_Company_Name_With_Account_Name</targetReference>
            </connector>
            <label>Update JPN Company Name - Account Name Changed</label>
        </rules>
        <rules>
            <name>Update_JPN_Company_Name_Account_Code_Changed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Formula_Account_Code_Changed</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_JPN_Company_Name_With_Account_Name</targetReference>
            </connector>
            <label>Update JPN Company Name - Account Code Changed</label>
        </rules>
    </decisions>
    <decisions>
        <name>Decision_ShippingCountry_Clear</name>
        <label>ShippingCountry Clear Check (All JPN Accounts)</label>
        <locationX>176</locationX>
        <locationY>719</locationY>
        <defaultConnectorLabel>ShippingCountry Already Blank</defaultConnectorLabel>
        <rules>
            <name>ShippingCountry_Not_Blank</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Formula_ShippingCountry_Not_Blank</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Clear_ShippingCountry</targetReference>
            </connector>
            <label>ShippingCountry Not Blank</label>
        </rules>
    </decisions>
    <description>After Save flow to clear ShippingCountry field and update JPN Company Name for JPN accounts.

        BUSINESS LOGIC:
        - ShippingCountry clearing: ALL JPN accounts (matches original JJ_JPN_CountryNameblank workflow rule)
        - JPN Company Name updates: Only Store records (matches original SW_Update_JPN_Company_Name process builder)

        TECHNICAL FEATURES:
        - Early exit optimization prevents unnecessary processing
        - No-op guards prevent recursive triggers and unnecessary DML
        - Explicit $Record__Prior comparisons (XML-safe, no merge tokens)
        - ISNEW + not-blank handling for newly created records
        - Zero SOQL queries to prevent governor limit issues</description>
    <formulas>
        <name>Formula_Account_Name_Changed</name>
        <dataType>Boolean</dataType>
        <expression>AND(
            $Record.CountryCode__c = "JPN",
            $Record.RecordType.DeveloperName = "Store",
            $Record.Name &lt;&gt; $Record__Prior.Name,
            OR(
                ISBLANK($Record.JJ_JPN_JPNCompanyName__c),
                $Record.JJ_JPN_JPNCompanyName__c &lt;&gt; $Record.Name
            )
        )</expression>
    </formulas>
    <formulas>
        <name>Formula_Account_Code_Changed</name>
        <dataType>Boolean</dataType>
        <expression>AND(
            $Record.CountryCode__c = "JPN",
            $Record.RecordType.DeveloperName = "Store",
            OR(
                $Record.JJ_JPN_Account_CODE__c &lt;&gt; $Record__Prior.JJ_JPN_Account_CODE__c,
                AND(
                    ISBLANK($Record__Prior.Id),
                    NOT(ISBLANK($Record.JJ_JPN_Account_CODE__c))
                )
            ),
            OR(
                ISBLANK($Record.JJ_JPN_JPNCompanyName__c),
                $Record.JJ_JPN_JPNCompanyName__c &lt;&gt; $Record.Name
            )
        )</expression>
    </formulas>
    <formulas>
        <name>Formula_Something_To_Process</name>
        <dataType>Boolean</dataType>
        <expression>OR(
            $Record.Name &lt;&gt; $Record__Prior.Name,
            $Record.JJ_JPN_Account_CODE__c &lt;&gt; $Record__Prior.JJ_JPN_Account_CODE__c,
            NOT(ISBLANK($Record.ShippingCountry)),
            ISBLANK($Record__Prior.Id)
        )</expression>
    </formulas>
    <formulas>
        <name>Formula_ShippingCountry_Not_Blank</name>
        <dataType>Boolean</dataType>
        <expression>NOT(ISBLANK($Record.ShippingCountry))</expression>
    </formulas>

    <interviewLabel>JJ_JPN Account AfterSave V2 {!$Flow.CurrentDateTime}</interviewLabel>
    <label>JJ_JPN Account AfterSave V2</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Update_Clear_ShippingCountry</name>
        <label>Clear ShippingCountry</label>
        <locationX>44</locationX>
        <locationY>827</locationY>
        <inputAssignments>
            <field>ShippingCountry</field>
            <value>
                <stringValue></stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_JPN_Company_Name_With_Account_Name</name>
        <label>Update JPN Company Name With Account Name</label>
        <locationX>44</locationX>
        <locationY>611</locationY>
        <connector>
            <targetReference>Decision_ShippingCountry_Clear</targetReference>
        </connector>
        <inputAssignments>
            <field>JJ_JPN_JPNCompanyName__c</field>
            <value>
                <elementReference>$Record.Name</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>

    <start>
        <locationX>176</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Decision_Early_Exit_Check</targetReference>
        </connector>
        <filterFormula>$Record.CountryCode__c = "JPN"</filterFormula>
        <object>Account</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
