/** 
* File Name: <PERSON><PERSON>_<PERSON>N_EditFromCMRExtTest 
* Description : Test Class for JJ_JPN_EditFromCMRExt - Business logic applied for Customer Master Regisrtion.
* Main Class : JJ_JPN_EditFromCMRExt
* Copyright : <PERSON>
* <AUTHOR> <PERSON> | <EMAIL> | <EMAIL>
* 
* Modification Log 
* =============================================================== 
*    Ver  |Date         |Author                |Modification
*    1.0  |20-Oct-2016  |@<EMAIL> |New Class created
*/ 
@IsTest
public class JJ_JPN_EditFromCMRExtTest 
{
	 public static Date todayDate = getValidActivationDate();

	 /**
	  * @description Helper method to get a valid activation date for test data
	  * Returns a future date that avoids validation rule restrictions
	  * @return Date A valid activation date for testing
	  */
	 private static Date getValidActivationDate() {
	     Date testDate = Date.today() + 2; // Start with day after tomorrow

	     // If it's a Monday, move to Tuesday
	     DateTime testDateTime = DateTime.newInstance(testDate, Time.newInstance(0, 0, 0, 0));
	     if (testDateTime.format('E') == 'Mon') {
	         testDate = testDate + 1;
	     }

	     return testDate;
	 }
	 
    static testMethod void createCMR()
    {
        Test.startTest(); 
        
        JJ_JPN_CustomerMasterRequest__c CMR =  new JJ_JPN_CustomerMasterRequest__c();
        CMR.Name = 'CMRAccout';
        CMR.JJ_JPN_StatusCode__c = 'C';
        CMR.Account_Activation_Start_Date__c = todayDate;
		CMR.JJ_JPN_SubCustomerGroup__c = 'SCG000006';
        insert CMR;
        system.debug('CMR List==>'+CMR);
   
        
        Account acc = new Account();
        acc.Name = 'AccName';
        acc.JJ_JPN_CustomerNameKana__c = 'Kana';
        acc.OutletNumber__c = '20001';
        acc.AccountNumber = '20001';
        acc.JJ_JPN_BillToCode__c = '10001';
        insert acc;
     
        // creating the ID for the loading class  and intializes id to class , for the beginnning of the page
        Apexpages.currentPage().getParameters().put('cmrId',CMR.id);
        
        ApexPages.StandardController sc = new ApexPages.StandardController(CMR);
        JJ_JPN_EditFromCMRExt CMRCls = new JJ_JPN_EditFromCMRExt(sc);
        CMRCls.getstatusCodeValues();
        CMRCls.statusCode='C';    
        CMRCls.CMR.Account_Activation_Start_Date__c = todayDate;
        CMRCls.doSave();
        CMRCls.cancel();
        
        system.assertEquals(CMR.Name,CMR.Name ,'HURRY ITS WRONG OUTPUT');
        system.assertNotEquals('CMR',CMR.Name ,'HURRY ITS WRONG OUTPUT');
        system.assert(CMR.Name==CMR.Name ,'HURRY ITS WRONG OUTPUT');
        
        
        Test.stopTest();
    }
    
    static testMethod void createCMR2()
    {
        Test.startTest(); 
        
        JJ_JPN_CustomerMasterRequest__c CMR =  new JJ_JPN_CustomerMasterRequest__c();
        CMR.Name = 'CMRAccout';
        CMR.JJ_JPN_StatusCode__c = 'C';
        CMR.Account_Activation_Start_Date__c = todayDate;
		CMR.JJ_JPN_SubCustomerGroup__c = 'SCG000006';
        insert CMR;
        system.debug('CMR List==>'+CMR);
   
        
        Account acc = new Account();
        acc.Name = 'AccName';
        acc.JJ_JPN_CustomerNameKana__c = 'Kana';
        acc.OutletNumber__c = '20001';
        acc.AccountNumber = '20001';
        acc.JJ_JPN_BillToCode__c = '10001';
        insert acc;
     
        // creating the ID for the loading class  and intializes id to class , for the beginnning of the page
        Apexpages.currentPage().getParameters().put('cmrId','');
        
        ApexPages.StandardController sc = new ApexPages.StandardController(CMR);
        JJ_JPN_EditFromCMRExt CMRCls = new JJ_JPN_EditFromCMRExt(sc);
        CMRCls.getstatusCodeValues();
        CMRCls.statusCode='C';    
        CMRCls.CMR.Account_Activation_Start_Date__c = todayDate;
        CMRCls.doSave();
        CMRCls.cancel();
        
        system.assertEquals(CMR.Name,CMR.Name ,'HURRY ITS WRONG OUTPUT');
        system.assertNotEquals('CMR',CMR.Name ,'HURRY ITS WRONG OUTPUT');
        system.assert(CMR.Name==CMR.Name ,'HURRY ITS WRONG OUTPUT');
        
        
        Test.stopTest();
    }
}