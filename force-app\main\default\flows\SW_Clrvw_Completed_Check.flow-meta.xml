<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <description>Component to check for the country</description>
        <name>CountryCode</name>
        <label>CountryCode</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>CountryCheckTrue</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>CountryEqualsJapan</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>mainUpdate</targetReference>
            </connector>
            <label>CountryCheckTrue</label>
        </rules>
    </decisions>
    <description>To update a field completed check based on few conditions (Workflow Migration)</description>
    <environments>Default</environments>
    <formulas>
        <description>Formula which evaluates true when country is Japan</description>
        <name>CountryEqualsJapan</name>
        <dataType>Boolean</dataType>
        <expression>{!$Record.Country__c} = &apos;Japan&apos;</expression>
    </formulas>
    <interviewLabel>Completed Check {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Completed Check</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <description>to update fields based on the conditon.(Workflow Migration).</description>
        <name>mainUpdate</name>
        <label>mainUpdate</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputAssignments>
            <field>CompletedCheck__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>CountryCode</targetReference>
        </connector>
        <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ActivityStatus__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </filters>
        <object>Event</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
