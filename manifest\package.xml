<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Package xmlns="http://soap.sforce.com/2006/04/metadata">
	<types>
		<members>*</members>
		<name>ApexClass</name>
	</types>
	<types>
		<members>*</members>
		<name>ApexComponent</name>
	</types>
	<types>
		<members>*</members>
		<name>ApexPage</name>
	</types>
	<types>
		<members>*</members>
		<name>ApexTestSuite</name>
	</types>
	<types>
		<members>*</members>
		<name>ApexTrigger</name>
	</types>
	<types>
		<members>*</members>
		<name>AuraDefinitionBundle</name>
	</types>
	<types>
		<members>Account</members>
		<members>AccountAnswer__c</members>
		<members>AccountBooking__c</members>
		<members>AccountBrand</members>
		<members>AccountContactRelation</members>
		<members>AccountContactRole</members>
		<members>AccountContactRole__c</members>
		<members>AccountGrade__c</members>
		<members>AccountInfo__c</members>
		<members>AccountName__c</members>
		<members>AccountPayInfo__c</members>
		<members>AccountPointHistory__c</members>
		<members>AccountQuestion__c</members>
		<members>Account_Msg__c</members>
		<members>Account__c</members>
		<members>ActionPlan</members>
		<members>ActionPlanItemDependency</members>
		<members>ActionPlnTmplItmDependency</members>
		<members>Activity</members>
		<members>Address</members>
		<members>AdminCommonCode__c</members>
		<members>AdminInfo__c</members>
		<members>AnalyticsUserAttrFuncTkn</members>
		<members>Answer__c</members>
		<members>AppointmentECP__c</members>
		<members>ApprovalSubmission</members>
		<members>ApprovalSubmissionDetail</members>
		<members>ApprovalWorkItem</members>
		<members>Asset</members>
		<members>AssetRelationship</members>
		<members>AssistantProgress</members>
		<members>AssociatedLocation</members>
		<members>AuditTrailFileExport</members>
		<members>AuthNumber__c</members>
		<members>AuthorizationForm</members>
		<members>AuthorizationFormConsent</members>
		<members>AuthorizationFormDataUse</members>
		<members>AuthorizationFormText</members>
		<members>AutoNumberCMR__c</members>
		<members>B2B_Custom_Exceptions__c</members>
		<members>B2B_Generic_Account__c</members>
		<members>BatchJob</members>
		<members>BatchJobPart</members>
		<members>BatchJobPartFailedRecord</members>
		<members>Batch_Details__c</members>
		<members>BoardList__c</members>
		<members>BrandName__c</members>
		<members>Brand_Code__c</members>
		<members>BusinessBrand</members>
		<members>CMR_SupportEmail_Setting__mdt</members>
		<members>CMTD__EnhancedRelatedList__mdt</members>
		<members>CPT_External_Field__c</members>
		<members>CPT_Statistics__c</members>
		<members>CalcMatrixColumnRange</members>
		<members>CalcProcStepRelationship</members>
		<members>CalculationMatrix</members>
		<members>CalculationMatrixColumn</members>
		<members>CalculationMatrixRow</members>
		<members>CalculationMatrixVersion</members>
		<members>CalculationProcedure</members>
		<members>CalculationProcedureStep</members>
		<members>CalculationProcedureVariable</members>
		<members>CalculationProcedureVersion</members>
		<members>Call_Scripts__c</members>
		<members>Campaign</members>
		<members>CampaignMember</members>
		<members>Canvas_CountryCodes__c</members>
		<members>Canvas_Geolocation_CountryCodes__c</members>
		<members>Canvas_Outlet_Email_CountryCodes__c</members>
		<members>Case</members>
		<members>CaseContactRole</members>
		<members>Categories_Tag__c</members>
		<members>ChatterActivity</members>
		<members>CloudSign__BatchLog__c</members>
		<members>CloudSign__BatchSettingDetail__c</members>
		<members>CloudSign__BatchSetting__c</members>
		<members>CloudSign__CloudSignApiMessage__mdt</members>
		<members>CloudSign__CloudSignControl__c</members>
		<members>CloudSign__CloudSignSendToSetting__c</members>
		<members>CloudSign__CloudSignSetting__c</members>
		<members>CloudSign__DocumentDetails__c</members>
		<members>CloudSign__DocumentSendHistory__c</members>
		<members>CloudSign__DocumentSendStatus__c</members>
		<members>CloudSign__Document__c</members>
		<members>CloudSign__FileInputField__c</members>
		<members>CloudSign__File__c</members>
		<members>CloudSign__Participant__c</members>
		<members>CloudSign__ReceiverInputField__c</members>
		<members>CloudSign__RecipientInputInformationMapping__mdt</members>
		<members>CloudSign__RecipientUpload__c</members>
		<members>CloudSign__Reportee__c</members>
		<members>CloudSign__SendDocumentFileSelectionLibrarySetting__mdt</members>
		<members>CloudSign__SendDocumentInitialDisplayField__mdt</members>
		<members>CloudSign__SendDocumentInitialDisplayMapping__mdt</members>
		<members>CloudSign__SendSearchObjectSetting__mdt</members>
		<members>CloudSign__TemplateFile__c</members>
		<members>CloudSign__TemplateParticipant__c</members>
		<members>CloudSign__TemplateWidget__c</members>
		<members>CloudSign__Template__c</members>
		<members>CloudSign__UploadRequest__c</members>
		<members>CloudSign__UserSetting__c</members>
		<members>CloudSign__WorkflowApprovedFile__c</members>
		<members>CloudSign__WorkflowFileExclusionSetting__mdt</members>
		<members>CloudSign__WorkflowItemEditabilitySetting__mdt</members>
		<members>CloudSign__WorkflowStatusReferenceSetting__mdt</members>
		<members>CloudSign__WorkflowUsageSetting__mdt</members>
		<members>Clustpl__CL_M_ActivityBasicScore__c</members>
		<members>Clustpl__CL_M_AutoGenerateRelationRule__c</members>
		<members>Clustpl__CL_M_AutoGenerateRelationTerm__c</members>
		<members>Clustpl__CL_M_BatchGenerateNumber__c</members>
		<members>Clustpl__CL_M_DisplaySetting__c</members>
		<members>Clustpl__CL_M_Message__c</members>
		<members>Clustpl__CL_M_NodeFieldDefine__c</members>
		<members>Clustpl__CL_M_NodeObjTransfer__c</members>
		<members>Clustpl__CL_M_ObjectTriggerConfigs__c</members>
		<members>Clustpl__CL_M_RelationImp__c</members>
		<members>Clustpl__CL_M_RelationTp__c</members>
		<members>Clustpl__CL_S_ErrorLog__c</members>
		<members>Clustpl__CL_T_Node__c</members>
		<members>Clustpl__CL_T_Relation__c</members>
		<members>Coaching_Field_Observations__c</members>
		<members>Coaching_Planning_Guide__c</members>
		<members>Coaching__c</members>
		<members>CollaborationGroup</members>
		<members>CollaborationGroupMember</members>
		<members>CommSubscription</members>
		<members>CommSubscriptionChannelType</members>
		<members>CommSubscriptionConsent</members>
		<members>CommSubscriptionTiming</members>
		<members>CommonCode__c</members>
		<members>Configuration_Setting__c</members>
		<members>ConsumerLevel__c</members>
		<members>Consumer_Relation__c</members>
		<members>Contact</members>
		<members>ContactPointAddress</members>
		<members>ContactPointConsent</members>
		<members>ContactPointEmail</members>
		<members>ContactPointPhone</members>
		<members>ContactPointTypeConsent</members>
		<members>ContactRequest</members>
		<members>ContentVersion</members>
		<members>Contract</members>
		<members>ContractContactRole</members>
		<members>ConversationApiLog</members>
		<members>ConversationApiLogObjSum</members>
		<members>Copy_Paste_Tool__c</members>
		<members>CountObject__c</members>
		<members>CouponContact__c</members>
		<members>CouponSendList__c</members>
		<members>Coupon__c</members>
		<members>CourseAssignment__c</members>
		<members>CourseTagAssignment__c</members>
		<members>Course__c</members>
		<members>Credientials__c</members>
		<members>CurriculumAssignment__c</members>
		<members>Curriculum__c</members>
		<members>CustomException__c</members>
		<members>Custom_Event__c</members>
		<members>Custom_Exception_JJSVM__mdt</members>
		<members>Custom_Profile_Ids__c</members>
		<members>Customer</members>
		<members>Customer_Segmentation_Master__c</members>
		<members>Customer_Segmentation_Matrix__c</members>
		<members>DataUseLegalBasis</members>
		<members>DataUsePurpose</members>
		<members>DecisionTblFileImportData</members>
		<members>DuplicateRecordItem</members>
		<members>DuplicateRecordSet</members>
		<members>DxOrderProduct__c</members>
		<members>DxOrder__c</members>
		<members>DxTotalQuantity__c</members>
		<members>ECPLocatorSettings__mdt</members>
		<members>ETRIAL_Trigger_Send_setting__c</members>
		<members>EmailMessage</members>
		<members>Email_Triggersends__c</members>
		<members>EmpUserProvisionProcessErr</members>
		<members>EngagementAttendee</members>
		<members>EngagementChannelType</members>
		<members>EngagementInteraction</members>
		<members>EngagementInterface</members>
		<members>EngagementTopic</members>
		<members>Etrial__c</members>
		<members>Event</members>
		<members>Event_Attendee__c</members>
		<members>Event_RecordType_Setting__c</members>
		<members>ExactTarget_Integration_Settings__c</members>
		<members>ExchangeUserMapping</members>
		<members>ExpressionFilter</members>
		<members>ExpressionFilterCriteria</members>
		<members>ExpressionSet</members>
		<members>ExpressionSetVersion</members>
		<members>ExternalEventMapping</members>
		<members>FeedItem</members>
		<members>Field_Trip__Field_Analysis__c</members>
		<members>Field_Trip__Field_Analytic_Config__c</members>
		<members>Field_Trip__Logistics__c</members>
		<members>Field_Trip__Object_Analysis__c</members>
		<members>FlowOrchestrationInstance</members>
		<members>FlowOrchestrationLog</members>
		<members>FlowOrchestrationStageInstance</members>
		<members>FlowOrchestrationStepInstance</members>
		<members>FlowOrchestrationWorkItem</members>
		<members>FocOrderProduct__c</members>
		<members>FocOrder__c</members>
		<members>FriendsAppRecommend__c</members>
		<members>Image</members>
		<members>InActiveCoupon0Doller__c</members>
		<members>Individual</members>
		<members>Instructor_Assignment__c</members>
		<members>Instructor__c</members>
		<members>IntegrationProviderAttr</members>
		<members>IntegrationProviderDef</members>
		<members>JJVCON_Custom_Exception__mdt</members>
		<members>JJVC_Contact_Recordtype__mdt</members>
		<members>JJVPRO_Custom_exceptions__mdt</members>
		<members>JJVPRO_Email_campaigns__mdt</members>
		<members>JJ_JPN_AccountPlan__c</members>
		<members>JJ_JPN_Account_for_Call_Dashboard__c</members>
		<members>JJ_JPN_ActionPlan__c</members>
		<members>JJ_JPN_ActionPlanforAP__c</members>
		<members>JJ_JPN_Address__c</members>
		<members>JJ_JPN_AreaPlan__c</members>
		<members>JJ_JPN_Assessment__c</members>
		<members>JJ_JPN_BatchJobQuerySettings__c</members>
		<members>JJ_JPN_CustomerMasterRequest__c</members>
		<members>JJ_JPN_ECPContact__c</members>
		<members>JJ_JPN_Event_for_Call_Dashboard__c</members>
		<members>JJ_JPN_FieldHistoryTracking__c</members>
		<members>JJ_JPN_Forecast__c</members>
		<members>JJ_JPN_Hospital__c</members>
		<members>JJ_JPN_HospitaltoDoctors__c</members>
		<members>JJ_JPN_InitialStock__c</members>
		<members>JJ_JPN_Mystery_Shopping__c</members>
		<members>JJ_JPN_NBA_Recommend__c</members>
		<members>JJ_JPN_Price_Information__c</members>
		<members>JJ_JPN_PrimaryAccountPlan__c</members>
		<members>JJ_JPN_PrimaryAreaPlan__c</members>
		<members>JJ_JPN_ProdCode_Classification_Mapping__mdt</members>
		<members>JJ_JPN_RecordTypeMapping__mdt</members>
		<members>JJ_JPN_StoreHospitalRelation__c</members>
		<members>JJ_JPN_Work_Codelist__c</members>
		<members>JJ_JPN_Work_Contact__c</members>
		<members>JJ_JPN_Work_DSpecialistConference__c</members>
		<members>JJ_JPN_Work_DoctorConference__c</members>
		<members>JJ_JPN_Work_Hospital__c</members>
		<members>JJ_JPN_Work_Workplace__c</members>
		<members>JnJDSF4__Cat_Content_Junction__c</members>
		<members>JnJDSF4__CategoryMobileConfig__c</members>
		<members>JnJDSF4__Category__c</members>
		<members>JnJDSF4__ContentReview__c</members>
		<members>JnJDSF4__DSA_App_Setting__c</members>
		<members>JnJDSF4__DSA_Playlist__c</members>
		<members>JnJDSF4__MobileAppConfig__c</members>
		<members>JnJDSF4__Playlist_Content_Junction__c</members>
		<members>KPI_Objectives__c</members>
		<members>Knowledge__kav</members>
		<members>LEXMAGICMOVER__NAMAttachmentConversionStatus__c</members>
		<members>LEXMAGICMOVER__NAMConfig__c</members>
		<members>LEXMAGICMOVER__NAMNoteConversionStatus__c</members>
		<members>LEXMAGICMOVER__NAMNoteConverted__c</members>
		<members>LMS_Settings__c</members>
		<members>LUT_County__c</members>
		<members>LUT_Region__c</members>
		<members>LUT_Territory__c</members>
		<members>LatestTDIndex__c</members>
		<members>Lead</members>
		<members>LinkedArticle</members>
		<members>Location</members>
		<members>LocationTrustMeasure</members>
		<members>LogData__c</members>
		<members>MA2_Account_Price_Group__c</members>
		<members>MA2_Address__c</members>
		<members>MA2_AppsFlyer_Connection__c</members>
		<members>MA2_AppsFlyer__c</members>
		<members>MA2_BlockHours__c</members>
		<members>MA2_CampaignDuration__c</members>
		<members>MA2_CampaignProducts__c</members>
		<members>MA2_ContactExcludedToSendAPIGEE__c</members>
		<members>MA2_CountryCodeLando__c</members>
		<members>MA2_CountryCode__c</members>
		<members>MA2_CountryWiseConfiguration__c</members>
		<members>MA2_Country_Currency_Map__c</members>
		<members>MA2_CouponCampaign__c</members>
		<members>MA2_CouponImage__c</members>
		<members>MA2_Coupon_For_Profile_Access__c</members>
		<members>MA2_Course__c</members>
		<members>MA2_CoursesAttended__c</members>
		<members>MA2_DeletedConsumersAnswers__c</members>
		<members>MA2_ECPLoginHistory__c</members>
		<members>MA2_Error_Log__c</members>
		<members>MA2_HDReportDLGroup__c</members>
		<members>MA2_HKCouponList__c</members>
		<members>MA2_HKCouponsList__c</members>
		<members>MA2_HKG_Repurchase__c</members>
		<members>MA2_IDNCouponList__c</members>
		<members>MA2_INDBrands__c</members>
		<members>MA2_InactiveCouponExclusion__c</members>
		<members>MA2_India_Validated_Promocodes__c</members>
		<members>MA2_Lifestyle_Rewards__c</members>
		<members>MA2_MYCouponList__c</members>
		<members>MA2_MultiPackPromotions__c</members>
		<members>MA2_Notification__c</members>
		<members>MA2_Partner_Promo_Code__c</members>
		<members>MA2_PartnershipCouponStore__c</members>
		<members>MA2_PartnershipCoupon__c</members>
		<members>MA2_Partnership_Coupon_Code__c</members>
		<members>MA2_Points_By_Product__mdt</members>
		<members>MA2_ProfileAccess__c</members>
		<members>MA2_PromoCode_Account__c</members>
		<members>MA2_Promo_Code__c</members>
		<members>MA2_Referral_Coupon__c</members>
		<members>MA2_RelatedAccounts__c</members>
		<members>MA2_RemoteServices__c</members>
		<members>MA2_Repeated_Coupon_Campaign_Period__c</members>
		<members>MA2_RetryMechanism__c</members>
		<members>MA2_Reward_Image__c</members>
		<members>MA2_Reward__c</members>
		<members>MA2_SFMC_Level_1_Tracking__c</members>
		<members>MA2_SFMC_Level_2_Tracking__c</members>
		<members>MA2_SGCouponList__c</members>
		<members>MA2_Stock_Inventory_Percent__c</members>
		<members>MA2_THCouponList__c</members>
		<members>MA2_TH_Key_Accounts__c</members>
		<members>MA2_TWNProductGrouping__c</members>
		<members>MA2_Temp_Buzzebee_Contact__c</members>
		<members>MA2_TextContent__c</members>
		<members>MA2_TierCoupon__c</members>
		<members>MA2_TransactionProduct__c</members>
		<members>MA2_UserProfile__c</members>
		<members>MA2_tmp_SAP_Order__c</members>
		<members>MLModel</members>
		<members>MLModelFactor</members>
		<members>MLModelFactorComponent</members>
		<members>Macro</members>
		<members>MacroAction</members>
		<members>MacroInstruction</members>
		<members>MacroUsage</members>
		<members>Medical_Detail__c</members>
		<members>MessagingEndUser</members>
		<members>MessagingSession</members>
		<members>NPS__c</members>
		<members>NetworkMember</members>
		<members>NetworkMemberChunk</members>
		<members>NotificationRecipient__c</members>
		<members>Notification__c</members>
		<members>Opportunity</members>
		<members>OpportunityCompetitor</members>
		<members>OpportunityContactRole</members>
		<members>OpportunityLineItem</members>
		<members>OpportunityRelatedDeleteLog</members>
		<members>OptIdList__c</members>
		<members>OpticianBoard__c</members>
		<members>OpticianE__c</members>
		<members>Optician__c</members>
		<members>Order</members>
		<members>OrderItem</members>
		<members>OrgMetricScanResult</members>
		<members>OrgMetricScanSummary</members>
		<members>PITCHER__Discussion_Item_Discussed__c</members>
		<members>PITCHER__Discussion_Item__c</members>
		<members>PITCHER__Instance_Discussion_Items__c</members>
		<members>PITCHER__Instance__c</members>
		<members>PITCHER__Multichannel_Activity_Line__c</members>
		<members>PITCHER__Multichannel_Activity__c</members>
		<members>PITCHER__Pitcher_Activity_Discussion__c</members>
		<members>PITCHER__Pitcher_Activity__c</members>
		<members>PITCHER__Pitcher_Attendee__c</members>
		<members>PITCHER__Pitcher_Content__c</members>
		<members>PITCHER__Pitcher_Feedback__c</members>
		<members>PITCHER__Pitcher_Presentation__c</members>
		<members>PITCHER__Pitcher_Synch_Activity_Tracking__c</members>
		<members>PITCHER__Pitcher_Synch_Error__c</members>
		<members>PITCHER__Placeholder__c</members>
		<members>PITCHER__Sent_Message__c</members>
		<members>PITCHER__Settings__c</members>
		<members>PartnerRole</members>
		<members>PartyConsent</members>
		<members>Patient_Survey__c</members>
		<members>PointHistory__c</members>
		<members>Popup__c</members>
		<members>Portal_Settings__c</members>
		<members>Pricebook2</members>
		<members>PricebookEntry</members>
		<members>ProcessException</members>
		<members>Product2</members>
		<members>ProductBrand__c</members>
		<members>ProductCart__c</members>
		<members>ProductLotNo__c</members>
		<members>ProductPrice__c</members>
		<members>ProductRanking__c</members>
		<members>PromotionMessage__c</members>
		<members>PromptAction</members>
		<members>PromptError</members>
		<members>Purchase_History__c</members>
		<members>PushList__c</members>
		<members>PushMessageList__c</members>
		<members>Question__c</members>
		<members>QuickText</members>
		<members>QuickTextUsage</members>
		<members>Quote</members>
		<members>QuoteLineItem</members>
		<members>Reach_CallFrequency__c</members>
		<members>Reach_Quarterly__c</members>
		<members>Reach__c</members>
		<members>RecommendContact__c</members>
		<members>RecommendReceive__c</members>
		<members>Recommendation</members>
		<members>RecordAction</members>
		<members>RecordActnSelItemExtrc</members>
		<members>RecordAlert</members>
		<members>RecordAlertActionableTarget</members>
		<members>RecordMergeHistory</members>
		<members>ReportAnomalyEventStore</members>
		<members>Report_Center_Data__c</members>
		<members>Rewards__c</members>
		<members>Role__c</members>
		<members>SAPIntegrationSettings__c</members>
		<members>SW_Account_Profile__c</members>
		<members>SW_CalendarSharing__c</members>
		<members>SW_Clrvw_AccountPOAMapping__c</members>
		<members>SW_Clrvw_AccountPOAStatus__c</members>
		<members>SW_Clrvw_Account_POA_Mapping__mdt</members>
		<members>SW_Clrvw_ECP_PE_Training__c</members>
		<members>SW_Clrvw_EventCall__c</members>
		<members>SW_Clrvw_PE_Training__c</members>
		<members>SW_Clrvw_POAActivity__c</members>
		<members>SW_Clrvw_POACallRelation__c</members>
		<members>SW_Clrvw_POAObjective__c</members>
		<members>SW_Clrvw_POA__c</members>
		<members>SW_DocuSignTemplate__c</members>
		<members>SW_Event_Subject_Settings__mdt</members>
		<members>SW_PinnedListViews__c</members>
		<members>SW_SalesPerformanceSetting__mdt</members>
		<members>SW_SalesPerformance__c</members>
		<members>Sales_Detail__c</members>
		<members>Sales_History__c</members>
		<members>Satisfaction_Surveys__c</members>
		<members>Scorecard</members>
		<members>ScorecardAssociation</members>
		<members>ScorecardMetric</members>
		<members>Seller</members>
		<members>SessionEnrollment__c</members>
		<members>SessionSchedule__c</members>
		<members>Session__c</members>
		<members>SetupDataSynchronization</members>
		<members>SharingRecordCollection</members>
		<members>SharingRecordCollectionItem</members>
		<members>SharingRecordCollectionMember</members>
		<members>Site</members>
		<members>SocialPersona</members>
		<members>SocialPost</members>
		<members>Social_Channel__c</members>
		<members>Solution</members>
		<members>StreamActivityAccess</members>
		<members>StreamingChannel</members>
		<members>Subscription__c</members>
		<members>SupportEmail_Setting__mdt</members>
		<members>Survey</members>
		<members>SurveyAnswer__c</members>
		<members>SurveyInvitation</members>
		<members>SurveyPage</members>
		<members>SurveyQuestionChoice</members>
		<members>SurveyQuestionItem__c</members>
		<members>SurveyQuestionResponse</members>
		<members>SurveyQuestion__c</members>
		<members>SurveyResponse</members>
		<members>SurveySubject</members>
		<members>SurveyVersion</members>
		<members>Survey_Answer__c</members>
		<members>Survey_JJSVM__c</members>
		<members>Survey_Question_Choice_JJSVM__c</members>
		<members>Survey_Question_JJSVM__c</members>
		<members>Survey_Question_Response_JJSVM__c</members>
		<members>Survey_Response_JJSVM__c</members>
		<members>Survey__c</members>
		<members>TIP_Member__c</members>
		<members>TableauHostMapping</members>
		<members>Task</members>
		<members>TipsRaw__c</members>
		<members>Topic</members>
		<members>TopicAssignment</members>
		<members>TradeHistoryDetails__c</members>
		<members>TradeHistory__c</members>
		<members>TransactionTd__c</members>
		<members>Transaction__c</members>
		<members>TrialOptician__c</members>
		<members>TriggerHandler__c</members>
		<members>TwilioSF__BulkMessage__c</members>
		<members>TwilioSF__LogEvent__e</members>
		<members>TwilioSF__Log__c</members>
		<members>TwilioSF__Media__c</members>
		<members>TwilioSF__Message__c</members>
		<members>TwilioSF__Opt_In_Keyword__c</members>
		<members>TwilioSF__Opt_In__c</members>
		<members>TwilioSF__TwilioLogSettings__c</members>
		<members>TwilioSF__Twilio_Message_Status__e</members>
		<members>TwilioSF__Twilio_Personal_Number__c</members>
		<members>User</members>
		<members>UserCapabilityPreference</members>
		<members>UserExternalCredential</members>
		<members>UserLocalWebServerIdentity</members>
		<members>UserPrioritizedRecord</members>
		<members>UserProvisioningRequest</members>
		<members>User_Locale_Wise_Settings__mdt</members>
		<members>User_Synch_Settings__c</members>
		<members>ValidationRule__c</members>
		<members>Venue__c</members>
		<members>VideoCall</members>
		<members>VideoCallParticipant</members>
		<members>VideoCallRecording</members>
		<members>VoiceCall</members>
		<members>Web_Location__c</members>
		<members>WorkBadge</members>
		<members>WorkBadgeDefinition</members>
		<members>WorkThanks</members>
		<members>dsfs__CustomParameterMap__c</members>
		<members>dsfs__DocuSign_Envelope_Document__c</members>
		<members>dsfs__DocuSign_Envelope_Recipient__c</members>
		<members>dsfs__DocuSign_Envelope__c</members>
		<members>dsfs__DocuSign_Recipient_Status__c</members>
		<members>dsfs__DocuSign_Status__c</members>
		<members>dsfs__EnvelopeConfiguration__c</members>
		<members>dsfs__EnvelopeLocalization__c</members>
		<members>dsfs__Recipient__c</members>
		<members>et4ae5__AggregateLink__c</members>
		<members>et4ae5__Automated_Send__c</members>
		<members>et4ae5__Business_Unit__c</members>
		<members>et4ae5__Campaign_Member_Configuration__c</members>
		<members>et4ae5__Configuration__c</members>
		<members>et4ae5__ET4AEConfig__c</members>
		<members>et4ae5__ET4AE_Config__c</members>
		<members>et4ae5__Email_Linkage__c</members>
		<members>et4ae5__IndividualEmailResult__c</members>
		<members>et4ae5__IndividualLink__c</members>
		<members>et4ae5__JB_CDC_Event__e</members>
		<members>et4ae5__JB_Event__e</members>
		<members>et4ae5__JB_Flow_Event__e</members>
		<members>et4ae5__Journey__c</members>
		<members>et4ae5__MC_CDC_Journey__c</members>
		<members>et4ae5__SMSDefinition__c</members>
		<members>et4ae5__SMSJunction__c</members>
		<members>et4ae5__SendDefinition__c</members>
		<members>et4ae5__SendJunction__c</members>
		<members>et4ae5__SupportRequest__c</members>
		<members>et4ae5__Triggered_Send_Execution__c</members>
		<members>et4ae5__UEBU__c</members>
		<members>et4ae5__abTest__c</members>
		<members>guar__GUAR_Setting__mdt</members>
		<members>ltngsharing__PrivateTestObject__c</members>
		<members>ltngsharing__ReadOnlyTestObject__c</members>
		<members>pricebook__c</members>
		<members>sflib_AggrifyEventLog__c</members>
		<members>sflib_AggrifyId__mdt</members>
		<members>sflib_EventBus__e</members>
		<members>sflib_LogEntry__c</members>
		<members>sflib_LogEvent__e</members>
		<members>sflib_LogLevelFilter__c</members>
		<members>sflib_Settings__c</members>
		<name>CustomObject</name>
	</types>
	<types>
		<members>*</members>
		<name>LightningComponentBundle</name>
	</types>
	<types>
		<members>ASPAC JPN Sales Rep</members>
		<name>Profile</name>
	</types>
	<types>
		<members>*</members>
		<name>StaticResource</name>
	</types>
	<version>65.0</version>
</Package>