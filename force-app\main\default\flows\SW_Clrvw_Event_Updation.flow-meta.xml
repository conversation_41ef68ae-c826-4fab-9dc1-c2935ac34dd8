<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Action_to_get_Last_Call_Notes</name>
        <label>Action to get Last Call Notes</label>
        <locationX>402</locationX>
        <locationY>900</locationY>
        <actionName>SW_Clrvw_EventRecordFlow</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Update_Account</targetReference>
        </connector>
        <dataTypeMappings>
            <typeName>T__sourceObjCollection</typeName>
            <typeValue>Event</typeValue>
        </dataTypeMappings>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>sourceObjCollection</name>
            <value>
                <elementReference>Get_Event</elementReference>
            </value>
        </inputParameters>
        <nameSegment>SW_Clrvw_EventRecordFlow</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>SW_Clrvw_Insert_Event_Call</name>
        <label>Insert Event Call</label>
        <locationX>358</locationX>
        <locationY>384</locationY>
        <actionName>SW_Clrvw_EventCallRecordFlow</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>AssignRecurrsiveResource</targetReference>
        </connector>
        <dataTypeMappings>
            <typeName>U__output</typeName>
            <typeValue>SW_Clrvw_EventCall__c</typeValue>
        </dataTypeMappings>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>eventId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>isCreate</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>SW_Clrvw_EventCallRecordFlow</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>SW_Clrvw_Update_Event_Call_1</name>
        <label>Update Event Call</label>
        <locationX>622</locationX>
        <locationY>384</locationY>
        <actionName>SW_Clrvw_EventCallRecordFlow</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>AssignRecurrsiveResource</targetReference>
        </connector>
        <dataTypeMappings>
            <typeName>U__output</typeName>
            <typeValue>SW_Clrvw_EventCall__c</typeValue>
        </dataTypeMappings>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>eventId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>isCreate</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>SW_Clrvw_EventCallRecordFlow</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Update_Description</name>
        <label>Update Description Field</label>
        <locationX>50</locationX>
        <locationY>900</locationY>
        <actionName>SW_Clrvw_UpdateEventDescription</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Update_Description_Field</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>accountRec</name>
            <value>
                <elementReference>Get_Account_Record</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>EventRec</name>
            <value>
                <elementReference>$Record</elementReference>
            </value>
        </inputParameters>
        <nameSegment>SW_Clrvw_UpdateEventDescription</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>55.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assignment recurrsive resource in case of call jpn and commercial japan</description>
        <name>AssignRecurrsiveResource</name>
        <label>Assign Recurrsive Resource</label>
        <locationX>622</locationX>
        <locationY>576</locationY>
        <assignmentItems>
            <assignToReference>IsRecurrsive</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Descision_to_Update_Data</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>SW_Clrvw_AddCurrentEventCall</name>
        <label>Add current Event Call</label>
        <locationX>402</locationX>
        <locationY>1632</locationY>
        <assignmentItems>
            <assignToReference>SW_Clrvw_UpdatedEventCall.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>SW_Clrvw_GetCurrentEventCall.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>SW_Clrvw_UpdatedEventCall.SW_Clrvw_IsLastCall__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>UpdatedEventCalls</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>SW_Clrvw_UpdatedEventCall</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>SW_Clrvw_UpdateEventCall</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>SW_Clrvw_CheckOtherEventCallsToFalse</name>
        <label>Check other event calls to false</label>
        <locationX>490</locationX>
        <locationY>1332</locationY>
        <assignmentItems>
            <assignToReference>SW_Clrvw_UpdatedEventCall.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>SW_Clrvw_LoopToEditRecords.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>SW_Clrvw_UpdatedEventCall.SW_Clrvw_IsLastCall__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>UpdatedEventCalls</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>SW_Clrvw_UpdatedEventCall</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>SW_Clrvw_LoopToEditRecords</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Decision_box_for_Scheduled_Path</name>
        <label>Decision box for Scheduled Path</label>
        <locationX>1590</locationX>
        <locationY>276</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Check_for_Activity_Status</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.ActivityStatus__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Pending Completion</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Date_Submitted__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Activity_Status_To_Completed</targetReference>
            </connector>
            <label>Check for Activity Status</label>
        </rules>
    </decisions>
    <decisions>
        <name>Decision_for_Create_and_Updation_of_Event_Call</name>
        <label>Decision for Create and Updation of Event Call</label>
        <locationX>622</locationX>
        <locationY>276</locationY>
        <defaultConnector>
            <targetReference>AssignRecurrsiveResource</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Create_and_Update_Event_Call</name>
            <conditionLogic>1 AND (2 OR ((3 OR 4) AND 5))</conditionLogic>
            <conditions>
                <leftValueReference>$Record.CreatedDate</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>$Record.LastModifiedDate</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>SW_Clrvw_Call_Type</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>JJ_JPN_CustomerDevelopmentManagerCallJPN</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Commercial_JPN</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>IsRecurrsive</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>SW_Clrvw_Insert_Event_Call</targetReference>
            </connector>
            <label>Create Event Call</label>
        </rules>
        <rules>
            <name>Update_Event_Call</name>
            <conditionLogic>1 AND (2 OR 3 OR 4)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.CreatedDate</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>$Record.LastModifiedDate</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>SW_Clrvw_Call_Type</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>JJ_JPN_CustomerDevelopmentManagerCallJPN</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Commercial_JPN</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>SW_Clrvw_Update_Event_Call_1</targetReference>
            </connector>
            <label>Update Event Call</label>
        </rules>
    </decisions>
    <decisions>
        <name>Descision_to_Update_Data</name>
        <label>Descision to Update Data</label>
        <locationX>622</locationX>
        <locationY>684</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Update_Description_Field_Decision</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>SW_Clrvw_Call_Type</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Update_Description_Field</leftValueReference>
                <operator>WasVisited</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Account_Record</targetReference>
            </connector>
            <label>Update Description Field Decision</label>
        </rules>
        <rules>
            <name>Is_Call_Record_With_Change_in_Comments</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>IsCallCommentsChanged</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedDate</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>$Record.LastModifiedDate</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.ActivityStatus__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Completed</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Event</targetReference>
            </connector>
            <label>Is Call Record With Change in Comments</label>
        </rules>
        <rules>
            <name>ISChanged_In_Activity_Status</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.ActivityStatus__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>$Record__Prior.ActivityStatus__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.ActivityStatus__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Pending Completion</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>SW_Clrvw_Call_Type</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Event_Record_On_Planned_Completion_Status</targetReference>
            </connector>
            <label>ISChanged In Activity Status</label>
        </rules>
        <rules>
            <name>Populate_Last_Call_Outcome</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>SW_Clrvw_Call_Type</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.CreatedDate</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>$Record.LastModifiedDate</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Related_Account</targetReference>
            </connector>
            <label>Populate Last Call Outcome</label>
        </rules>
    </decisions>
    <description>AGFB - 20091 and AGFB - 20092 - &apos;Status not equal to Complete&apos; condition removed from &apos;Decision to Update Data&apos;</description>
    <environments>Default</environments>
    <formulas>
        <description>Last Call Notes from Event&apos;s Account</description>
        <name>Account_LastCallNotes</name>
        <dataType>String</dataType>
        <expression>IF ( CONTAINS ( {!Get_Related_Account.SW_Clrvw_Call_Notes__c}, &quot;Call Notes:&quot; ), 
MID ( {!Get_Related_Account.SW_Clrvw_Call_Notes__c}, FIND ( &quot;Call Notes:&quot;, {!Get_Related_Account.SW_Clrvw_Call_Notes__c}  )+12, LEN ( {!Get_Related_Account.SW_Clrvw_Call_Notes__c} ) ), &apos;&apos; )</expression>
    </formulas>
    <formulas>
        <name>IsCallCommentsChanged</name>
        <dataType>Boolean</dataType>
        <expression>AND({!$Record.RecordType.DeveloperName}=&apos;SW_Clrvw_Call_Type&apos;,ISCHANGED({!$Record.CallOutcomeCallTargetComments__c}))</expression>
    </formulas>
    <interviewLabel>Update Event {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Event Creation/Updation</label>
    <loops>
        <name>SW_Clrvw_LoopToEditRecords</name>
        <label>Loop to edit records</label>
        <locationX>402</locationX>
        <locationY>1224</locationY>
        <collectionReference>SW_Clrvw_GetAllRelatedLastCallOutcomeEventCallsExceptCurrentCall</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>SW_Clrvw_CheckOtherEventCallsToFalse</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>SW_Clrvw_GetCurrentEventCall</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Account_Record</name>
        <label>Get Account Record</label>
        <locationX>50</locationX>
        <locationY>792</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_Description</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Event</name>
        <label>Get Event</label>
        <locationX>402</locationX>
        <locationY>792</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Action_to_get_Last_Call_Notes</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Event</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get related Account of current Event</description>
        <name>Get_Related_Account</name>
        <label>Get Related Account</label>
        <locationX>930</locationX>
        <locationY>792</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_Event_Last_Call_Outcome</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>SW_Clrvw_GetAllRelatedLastCallOutcomeEventCallsExceptCurrentCall</name>
        <label>Get all related last call outcome event calls except current call</label>
        <locationX>402</locationX>
        <locationY>1116</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>SW_Clrvw_LoopToEditRecords</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SW_Clrvw_WhatId__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </filters>
        <filters>
            <field>SW_Clrvw_IsLastCall__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>SW_Crvw_RelatedEventId__c</field>
            <operator>NotEqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>SW_Clrvw_EventCall__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>SW_Clrvw_GetCurrentEventCall</name>
        <label>Get Current Event Call</label>
        <locationX>402</locationX>
        <locationY>1524</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>SW_Clrvw_AddCurrentEventCall</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SW_Crvw_RelatedEventId__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>SW_Clrvw_EventCall__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>SW_Clrvw_UpdateEventCall</name>
        <label>Update Event Call</label>
        <locationX>402</locationX>
        <locationY>1740</locationY>
        <inputReference>UpdatedEventCalls</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Account</name>
        <label>Update Account</label>
        <locationX>402</locationX>
        <locationY>1008</locationY>
        <connector>
            <targetReference>SW_Clrvw_GetAllRelatedLastCallOutcomeEventCallsExceptCurrentCall</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.AccountId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>SW_Clrvw_Call_Notes__c</field>
            <value>
                <elementReference>Action_to_get_Last_Call_Notes</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Activity_Status_To_Completed</name>
        <label>Update Activity Status To Completed</label>
        <locationX>1458</locationX>
        <locationY>384</locationY>
        <inputAssignments>
            <field>ActivityStatus__c</field>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Description_Field</name>
        <label>Update Description Field</label>
        <locationX>50</locationX>
        <locationY>1008</locationY>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>Descision_to_Update_Data</targetReference>
        </connector>
        <inputAssignments>
            <field>Description</field>
            <value>
                <elementReference>Update_Description</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update Event with last call outcome (with Related Account&apos;s Last Call Notes)</description>
        <name>Update_Event_Last_Call_Outcome</name>
        <label>Update Event Last Call Outcome</label>
        <locationX>930</locationX>
        <locationY>900</locationY>
        <inputAssignments>
            <field>SW_Clrvw_Notes_Prev_Call__c</field>
            <value>
                <elementReference>Account_LastCallNotes</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Event_Record_On_Planned_Completion_Status</name>
        <label>Update Event Record On Planned Completion Status</label>
        <locationX>666</locationX>
        <locationY>792</locationY>
        <inputAssignments>
            <field>Date_Submitted__c</field>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>980</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Decision_for_Create_and_Updation_of_Event_Call</targetReference>
        </connector>
        <object>Event</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <scheduledPaths>
            <name>Update_Activity_Status_after_24_hr</name>
            <connector>
                <targetReference>Decision_box_for_Scheduled_Path</targetReference>
            </connector>
            <label>Update Activity Status after 24 hr</label>
            <maxBatchSize>1</maxBatchSize>
            <offsetNumber>24</offsetNumber>
            <offsetUnit>Hours</offsetUnit>
            <recordField>Date_Submitted__c</recordField>
            <timeSource>RecordField</timeSource>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>EventCallRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>SW_Clrvw_EventCall__c</objectType>
    </variables>
    <variables>
        <description>Check weather current transaction is recurrsive or not</description>
        <name>IsRecurrsive</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>LastCallNotes</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>SW_Clrvw_UpdatedEventCall</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>SW_Clrvw_EventCall__c</objectType>
    </variables>
    <variables>
        <name>TextfromActionGetLastCallNotes</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>TextfromGetLastCallOutcome</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>UpdatedEventCalls</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>SW_Clrvw_EventCall__c</objectType>
    </variables>
</Flow>
